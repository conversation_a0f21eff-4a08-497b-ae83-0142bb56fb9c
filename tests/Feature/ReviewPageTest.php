<?php

namespace Tests\Feature;

use App\Services\QrCodeService;
use App\Services\ReviewService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;

class ReviewPageTest extends TestCase
{
    /**
     * Test that the review page loads correctly.
     *
     * @return void
     */
    public function test_review_page_loads_with_valid_qr_code()
    {
        // Mock the QrCodeService
        $qrCodeService = Mockery::mock(QrCodeService::class);
        $qrCodeService->shouldReceive('getQrCodeByUuid')
            ->once()
            ->with('test-uuid')
            ->andReturn([
                'id' => '1',
                'name' => 'Test Business',
                'description' => 'Test Description',
                'uuid' => 'test-uuid',
                'is_active' => true,
                'scan_count' => 0
            ]);
        
        $qrCodeService->shouldReceive('incrementScanCount')
            ->once()
            ->with('1')
            ->andR<PERSON>urn(true);
        
        // Mock the ReviewService
        $reviewService = Mockery::mock(ReviewService::class);
        
        // Bind the mocked services to the container
        $this->app->instance(QrCodeService::class, $qrCodeService);
        $this->app->instance(ReviewService::class, $reviewService);
        
        // Make the request
        $response = $this->get('/review/test-uuid');
        
        // Assert the response
        $response->assertStatus(200);
        $response->assertViewIs('pages.review.form');
        $response->assertViewHas('qrCode');
        $response->assertViewHas('businessName', 'Test Business');
    }
    
    /**
     * Test that the review page shows an error for invalid QR code.
     *
     * @return void
     */
    public function test_review_page_shows_error_with_invalid_qr_code()
    {
        // Mock the QrCodeService
        $qrCodeService = Mockery::mock(QrCodeService::class);
        $qrCodeService->shouldReceive('getQrCodeByUuid')
            ->once()
            ->with('invalid-uuid')
            ->andReturn(null);
        
        // Mock the ReviewService
        $reviewService = Mockery::mock(ReviewService::class);
        
        // Bind the mocked services to the container
        $this->app->instance(QrCodeService::class, $qrCodeService);
        $this->app->instance(ReviewService::class, $reviewService);
        
        // Make the request
        $response = $this->get('/review/invalid-uuid');
        
        // Assert the response
        $response->assertStatus(200);
        $response->assertViewIs('pages.review.error');
        $response->assertViewHas('message', 'QR code not found or has expired.');
    }
    
    /**
     * Test that a review can be submitted successfully.
     *
     * @return void
     */
    public function test_review_can_be_submitted()
    {
        // Mock the QrCodeService
        $qrCodeService = Mockery::mock(QrCodeService::class);
        $qrCodeService->shouldReceive('getQrCodeByUuid')
            ->once()
            ->with('test-uuid')
            ->andReturn([
                'id' => '1',
                'name' => 'Test Business',
                'description' => 'Test Description',
                'uuid' => 'test-uuid',
                'is_active' => true,
                'scan_count' => 0
            ]);
        
        // Mock the ReviewService
        $reviewService = Mockery::mock(ReviewService::class);
        $reviewService->shouldReceive('createReview')
            ->once()
            ->andReturn([
                'id' => '1',
                'qr_code_id' => '1',
                'customer_name' => 'Test Customer',
                'customer_email' => '<EMAIL>',
                'rating' => 4,
                'comment' => 'Test Comment',
                'additional_feedback' => 'Test Feedback',
                'is_published_to_google' => false
            ]);
        
        // Bind the mocked services to the container
        $this->app->instance(QrCodeService::class, $qrCodeService);
        $this->app->instance(ReviewService::class, $reviewService);
        
        // Make the request
        $response = $this->post('/review/test-uuid', [
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'rating' => 4,
            'comment' => 'Test Comment',
            'additional_feedback' => 'Test Feedback'
        ]);
        
        // Assert the response
        $response->assertStatus(200);
        $response->assertViewIs('pages.review.thank-you');
        $response->assertViewHas('showGoogleOption', false);
        $response->assertViewHas('businessName', 'Test Business');
    }
    
    /**
     * Test that a 5-star review shows Google option.
     *
     * @return void
     */
    public function test_five_star_review_shows_google_option()
    {
        // Mock the QrCodeService
        $qrCodeService = Mockery::mock(QrCodeService::class);
        $qrCodeService->shouldReceive('getQrCodeByUuid')
            ->once()
            ->with('test-uuid')
            ->andReturn([
                'id' => '1',
                'name' => 'Test Business',
                'description' => 'Test Description',
                'uuid' => 'test-uuid',
                'is_active' => true,
                'scan_count' => 0
            ]);
        
        // Mock the ReviewService
        $reviewService = Mockery::mock(ReviewService::class);
        $reviewService->shouldReceive('createReview')
            ->once()
            ->andReturn([
                'id' => '1',
                'qr_code_id' => '1',
                'customer_name' => 'Test Customer',
                'customer_email' => '<EMAIL>',
                'rating' => 5,
                'comment' => 'Test Comment',
                'additional_feedback' => null,
                'is_published_to_google' => false
            ]);
        
        // Bind the mocked services to the container
        $this->app->instance(QrCodeService::class, $qrCodeService);
        $this->app->instance(ReviewService::class, $reviewService);
        
        // Make the request
        $response = $this->post('/review/test-uuid', [
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'rating' => 5,
            'comment' => 'Test Comment'
        ]);
        
        // Assert the response
        $response->assertStatus(200);
        $response->assertViewIs('pages.review.thank-you');
        $response->assertViewHas('showGoogleOption', true);
        $response->assertViewHas('businessName', 'Test Business');
    }
    
    /**
     * Clean up after tests.
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
