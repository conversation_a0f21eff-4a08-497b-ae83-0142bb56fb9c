<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\Session;

class DashboardTest extends TestCase
{
    /**
     * Test that unauthenticated users are redirected from dashboard.
     *
     * @return void
     */
    public function test_unauthenticated_users_cannot_access_dashboard()
    {
        $response = $this->get('/dashboard');
        $response->assertRedirect('/login');
    }

    /**
     * Test that authenticated users can access dashboard.
     *
     * @return void
     */
    public function test_authenticated_users_can_access_dashboard()
    {
        // Mock Supabase authentication by setting session variables
        Session::put('user_id', 'test-user-id');
        Session::put('user_name', 'Test User');
        Session::put('user_email', '<EMAIL>');
        
        $response = $this->get('/dashboard');
        $response->assertStatus(200);
        $response->assertSee('Welcome back, Test User');
        $response->assertViewIs('pages.dashboard');
    }

    /**
     * Test that authenticated users can access QR codes page.
     *
     * @return void
     */
    public function test_authenticated_users_can_access_qr_codes_page()
    {
        // Mock Supabase authentication by setting session variables
        Session::put('user_id', 'test-user-id');
        Session::put('user_name', 'Test User');
        Session::put('user_email', '<EMAIL>');
        
        $response = $this->get('/dashboard/qr-codes');
        $response->assertStatus(200);
        $response->assertViewIs('pages.dashboard.qr-codes');
    }

    /**
     * Test that authenticated users can access reviews page.
     *
     * @return void
     */
    public function test_authenticated_users_can_access_reviews_page()
    {
        // Mock Supabase authentication by setting session variables
        Session::put('user_id', 'test-user-id');
        Session::put('user_name', 'Test User');
        Session::put('user_email', '<EMAIL>');
        
        $response = $this->get('/dashboard/reviews');
        $response->assertStatus(200);
        $response->assertViewIs('pages.dashboard.reviews');
    }

    /**
     * Test that authenticated users can access analytics page.
     *
     * @return void
     */
    public function test_authenticated_users_can_access_analytics_page()
    {
        // Mock Supabase authentication by setting session variables
        Session::put('user_id', 'test-user-id');
        Session::put('user_name', 'Test User');
        Session::put('user_email', '<EMAIL>');
        
        $response = $this->get('/dashboard/analytics');
        $response->assertStatus(200);
        $response->assertViewIs('pages.dashboard.analytics');
    }

    /**
     * Test that authenticated users can access templates page.
     *
     * @return void
     */
    public function test_authenticated_users_can_access_templates_page()
    {
        // Mock Supabase authentication by setting session variables
        Session::put('user_id', 'test-user-id');
        Session::put('user_name', 'Test User');
        Session::put('user_email', '<EMAIL>');
        
        $response = $this->get('/dashboard/templates');
        $response->assertStatus(200);
        $response->assertViewIs('pages.dashboard.templates');
    }
}
