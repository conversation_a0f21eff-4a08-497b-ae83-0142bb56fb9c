<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Str;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test login page loads correctly.
     *
     * @return void
     */
    public function test_login_page_loads()
    {
        $response = $this->get('/login');
        $response->assertStatus(200);
        $response->assertViewIs('pages.auth.login');
    }

    /**
     * Test register page loads correctly.
     *
     * @return void
     */
    public function test_register_page_loads()
    {
        $response = $this->get('/register');
        $response->assertStatus(200);
        $response->assertViewIs('pages.auth.register');
    }

    /**
     * Test user can register.
     *
     * @return void
     */
    public function test_user_can_register()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'terms' => 'on',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect('/dashboard');
    }
    
    /**
     * Test user can register with Supabase token.
     *
     * @return void
     */
    public function test_user_can_register_with_supabase()
    {
        // Mock the Supabase API response
        Http::fake([
            config('supabase.url') . '/auth/v1/user' => Http::response([
                'email' => '<EMAIL>',
                'user_metadata' => ['name' => 'Supabase User'],
            ], 200),
        ]);

        $response = $this->postJson('/register', [
            'name' => 'Supabase User',
            'email' => '<EMAIL>',
            'supabase_token' => 'fake-supabase-token',
            'terms' => true,
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Supabase User',
        ]);
    }

    /**
     * Test user can login.
     *
     * @return void
     */
    public function test_user_can_login()
    {
        $user = User::factory()->create();
        
        $response = $this->post('/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect('/dashboard');
    }
    
    /**
     * Test user can login with Supabase token.
     *
     * @return void
     */
    public function test_user_can_login_with_supabase()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        // Mock the Supabase API response
        Http::fake([
            config('supabase.url') . '/auth/v1/user' => Http::response([
                'email' => '<EMAIL>',
                'user_metadata' => ['name' => $user->name],
            ], 200),
        ]);

        $response = $this->postJson('/login', [
            'supabase_token' => 'fake-supabase-token',
            'remember' => true,
        ]);

        $response->assertStatus(200);
        $this->assertAuthenticated();
    }

    /**
     * Test user can logout.
     *
     * @return void
     */
    public function test_user_can_logout()
    {
        $user = User::factory()->create();
        
        $this->actingAs($user);
        $this->assertAuthenticated();
        
        $response = $this->post('/logout');
        
        $this->assertGuest();
        $response->assertRedirect('/');
    }
    
    /**
     * Test user can logout with JSON request.
     *
     * @return void
     */
    public function test_user_can_logout_with_json()
    {
        $user = User::factory()->create();
        
        $this->actingAs($user);
        $this->assertAuthenticated();
        
        $response = $this->postJson('/logout');
        
        $response->assertStatus(200);
        $this->assertGuest();
    }
    
    /**
     * Test API can get authenticated user.
     *
     * @return void
     */
    public function test_api_can_get_authenticated_user()
    {
        $user = User::factory()->create();
        
        $this->actingAs($user);
        
        $response = $this->getJson('/api/user');
        
        $response->assertStatus(200)
            ->assertJson([
                'authenticated' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ]
            ]);
    }
}
