<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SupabaseAuthTest extends TestCase
{
    /**
     * Test the login page loads correctly.
     *
     * @return void
     */
    public function test_login_page_loads()
    {
        $response = $this->get('/login');
        $response->assertStatus(200);
        $response->assertViewIs('pages.auth.login');
    }

    /**
     * Test the register page loads correctly.
     *
     * @return void
     */
    public function test_register_page_loads()
    {
        $response = $this->get('/register');
        $response->assertStatus(200);
        $response->assertViewIs('pages.auth.register');
    }

    /**
     * Test the dashboard page loads.
     *
     * @return void
     */
    public function test_dashboard_page_loads()
    {
        // Bypass middleware for testing
        $this->withoutMiddleware();
        
        $response = $this->get('/dashboard');
        $response->assertStatus(200);
        $response->assertViewIs('pages.dashboard');
    }

    /**
     * Test the Supabase auth controller methods exist.
     *
     * @return void
     */
    public function test_auth_controller_methods_exist()
    {
        // Test that the controller methods exist
        $this->assertTrue(method_exists(\App\Http\Controllers\AuthController::class, 'login'));
        $this->assertTrue(method_exists(\App\Http\Controllers\AuthController::class, 'register'));
        $this->assertTrue(method_exists(\App\Http\Controllers\AuthController::class, 'logout'));
    }
}
