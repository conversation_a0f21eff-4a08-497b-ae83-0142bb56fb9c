<?php

namespace Tests\Feature;

use App\Http\Controllers\HomeController;
use Tests\TestCase;

class LandingPageTest extends TestCase
{
    /**
     * Test that the home route is defined and points to the HomeController.
     */
    public function test_home_route_exists(): void
    {
        // Test that the route exists and is connected to the HomeController@index method
        $this->assertTrue(class_exists(HomeController::class), 'HomeController class exists');
        $this->assertTrue(method_exists(HomeController::class, 'index'), 'HomeController has index method');
        
        // Test that the route is defined
        $routes = app('router')->getRoutes();
        $this->assertTrue($routes->hasNamedRoute('home'), 'Named route "home" exists');
    }
    
    /**
     * Test that the login and register routes exist.
     */
    public function test_auth_routes_exist(): void
    {
        // Test that the login and register routes exist
        $routes = app('router')->getRoutes();
        $this->assertTrue($routes->hasNamedRoute('login'), 'Named route "login" exists');
        $this->assertTrue($routes->hasNamedRoute('register'), 'Named route "register" exists');
    }
}
