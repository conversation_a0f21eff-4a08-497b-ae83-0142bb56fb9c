<?php

namespace Tests\Unit\Services;

use App\Services\ReviewService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class ReviewServiceTest extends TestCase
{
    protected $reviewService;
    protected $mockUrl = 'https://mock-supabase-url.com';
    protected $mockKey = 'mock-supabase-key';
    protected $mockServiceKey = 'mock-service-key';
    protected $userId = '123e4567-e89b-12d3-a456-426614174000';

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the Supabase config
        Config::set('supabase.url', $this->mockUrl);
        Config::set('supabase.key', $this->mockKey);
        Config::set('supabase.service_role', $this->mockServiceKey);

        $this->reviewService = new ReviewService();
    }

    public function testGetReviewsForUser()
    {
        // Mock response data
        $mockReviews = [
            [
                'id' => '123e4567-e89b-12d3-a456-426614174001',
                'qr_code_id' => '123e4567-e89b-12d3-a456-426614174010',
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
                'rating' => 5,
                'comment' => 'Great service!',
                'vendor_response' => null,
                'created_at' => '2023-01-01T00:00:00Z',
                'updated_at' => '2023-01-01T00:00:00Z'
            ],
            [
                'id' => '123e4567-e89b-12d3-a456-426614174002',
                'qr_code_id' => '123e4567-e89b-12d3-a456-426614174011',
                'customer_name' => 'Jane Smith',
                'customer_email' => '<EMAIL>',
                'rating' => 4,
                'comment' => 'Good experience overall',
                'vendor_response' => 'Thank you for your feedback!',
                'created_at' => '2023-01-02T00:00:00Z',
                'updated_at' => '2023-01-03T00:00:00Z'
            ]
        ];

        // Mock SQL query response
        Http::fake([
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::response($mockReviews, 200)
        ]);

        // Call the method
        $result = $this->reviewService->getReviewsForUser($this->userId, [], 10, 0);

        // Assert the result
        $this->assertEquals($mockReviews, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/rpc/execute_sql" &&
                   $request->method() == 'POST' &&
                   $data['params']['user_id'] == $this->userId &&
                   $data['params']['limit'] == 10 &&
                   $data['params']['offset'] == 0 &&
                   strpos($data['query'], 'SELECT r.*') !== false &&
                   strpos($data['query'], 'FROM reviews r') !== false &&
                   strpos($data['query'], 'JOIN qr_codes q ON r.qr_code_id = q.id') !== false &&
                   strpos($data['query'], 'WHERE q.user_id = :user_id') !== false;
        });
    }

    public function testGetReviewsForUserWithFilters()
    {
        // Mock response data
        $mockReviews = [
            [
                'id' => '123e4567-e89b-12d3-a456-426614174001',
                'qr_code_id' => '123e4567-e89b-12d3-a456-426614174010',
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
                'rating' => 5,
                'comment' => 'Great service!',
                'vendor_response' => null,
                'created_at' => '2023-01-01T00:00:00Z',
                'updated_at' => '2023-01-01T00:00:00Z'
            ]
        ];

        // Create filters
        $filters = [
            'rating' => 5,
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-31'
        ];

        // Mock SQL query response
        Http::fake([
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::response($mockReviews, 200)
        ]);

        // Call the method
        $result = $this->reviewService->getReviewsForUser($this->userId, $filters, 10, 0);

        // Assert the result
        $this->assertEquals($mockReviews, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($filters) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/rpc/execute_sql" &&
                   $request->method() == 'POST' &&
                   $data['params']['user_id'] == $this->userId &&
                   $data['params']['rating'] == $filters['rating'] &&
                   $data['params']['start_date'] == $filters['start_date'] &&
                   $data['params']['end_date'] == $filters['end_date'] &&
                   $data['params']['limit'] == 10 &&
                   $data['params']['offset'] == 0 &&
                   strpos($data['query'], 'AND r.rating = :rating') !== false &&
                   strpos($data['query'], 'AND r.created_at BETWEEN :start_date AND :end_date') !== false;
        });
    }

    public function testCountReviewsForUser()
    {
        // Mock response data
        $mockCount = [['count' => 15]];

        // Mock SQL query response
        Http::fake([
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::response($mockCount, 200)
        ]);

        // Call the method
        $result = $this->reviewService->countReviewsForUser($this->userId);

        // Assert the result
        $this->assertEquals(15, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/rpc/execute_sql" &&
                   $request->method() == 'POST' &&
                   $data['params']['user_id'] == $this->userId &&
                   strpos($data['query'], 'SELECT COUNT(*) as count') !== false &&
                   strpos($data['query'], 'FROM reviews r') !== false &&
                   strpos($data['query'], 'JOIN qr_codes q ON r.qr_code_id = q.id') !== false &&
                   strpos($data['query'], 'WHERE q.user_id = :user_id') !== false;
        });
    }

    public function testAddResponseToReview()
    {
        // Mock response data
        $reviewId = '123e4567-e89b-12d3-a456-426614174001';
        $response = 'Thank you for your feedback!';
        $mockResponse = [
            'id' => $reviewId,
            'qr_code_id' => '123e4567-e89b-12d3-a456-426614174010',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'rating' => 5,
            'comment' => 'Great service!',
            'vendor_response' => $response,
            'created_at' => '2023-01-01T00:00:00Z',
            'updated_at' => '2023-01-03T00:00:00Z'
        ];

        // Mock HTTP response
        Http::fake([
            "{$this->mockUrl}/rest/v1/reviews*" => Http::response($mockResponse, 200)
        ]);

        // Call the method
        $result = $this->reviewService->addResponseToReview($reviewId, $response);

        // Assert the result
        $this->assertEquals($mockResponse, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($reviewId, $response) {
            return $request->url() == "{$this->mockUrl}/rest/v1/reviews" &&
                   $request->method() == 'PATCH' &&
                   $request['id'] == 'eq.' . $reviewId &&
                   json_decode($request->body(), true) == ['vendor_response' => $response];
        });
    }

    public function testGetReviewStats()
    {
        // Mock response data for count query
        $mockCount = [['count' => 20]];
        
        // Mock response data for rating distribution
        $mockRatingDistribution = [
            ['rating' => 1, 'count' => 1],
            ['rating' => 2, 'count' => 2],
            ['rating' => 3, 'count' => 3],
            ['rating' => 4, 'count' => 4],
            ['rating' => 5, 'count' => 10]
        ];
        
        // Mock response data for responded count
        $mockRespondedCount = [['count' => 15]];
        
        // Mock response data for published count
        $mockPublishedCount = [['count' => 5]];

        // Mock HTTP responses for different queries
        Http::fake([
            // For countReviewsForUser
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::sequence()
                ->push($mockCount, 200)
                ->push($mockRatingDistribution, 200)
                ->push($mockRespondedCount, 200)
                ->push($mockPublishedCount, 200)
        ]);

        // Call the method
        $result = $this->reviewService->getReviewStats($this->userId);

        // Expected result
        $expected = [
            'total' => 20,
            'average_rating' => 4.0, // (1*1 + 2*2 + 3*3 + 4*4 + 5*10) / 20
            'rating_distribution' => [
                1 => 1,
                2 => 2,
                3 => 3,
                4 => 4,
                5 => 10
            ],
            'responded' => 15,
            'published_to_google' => 5
        ];

        // Assert the result
        $this->assertEquals($expected, $result);
    }

    public function testGetMonthlyReviewCounts()
    {
        // Mock response data
        $mockMonthlyData = [
            ['month' => '2023-01-01T00:00:00Z', 'count' => 5],
            ['month' => '2023-02-01T00:00:00Z', 'count' => 10],
            ['month' => '2023-03-01T00:00:00Z', 'count' => 15]
        ];

        // Mock SQL query response
        Http::fake([
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::response($mockMonthlyData, 200)
        ]);

        // Call the method
        $result = $this->reviewService->getMonthlyReviewCounts($this->userId, 3);

        // Expected result (formatted months)
        $expected = [
            ['month' => 'Jan 2023', 'count' => 5],
            ['month' => 'Feb 2023', 'count' => 10],
            ['month' => 'Mar 2023', 'count' => 15]
        ];

        // Assert the result has the right structure
        $this->assertCount(3, $result);
        $this->assertArrayHasKey('month', $result[0]);
        $this->assertArrayHasKey('count', $result[0]);

        // Assert the request was made correctly
        Http::assertSent(function ($request) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/rpc/execute_sql" &&
                   $request->method() == 'POST' &&
                   $data['params']['user_id'] == $this->userId &&
                   $data['params']['months'] == 3 &&
                   strpos($data['query'], 'DATE_TRUNC(\'month\', r.created_at) as month') !== false &&
                   strpos($data['query'], 'COUNT(*) as count') !== false &&
                   strpos($data['query'], 'GROUP BY DATE_TRUNC(\'month\', r.created_at)') !== false;
        });
    }
}
