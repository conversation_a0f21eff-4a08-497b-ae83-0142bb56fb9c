<?php

namespace Tests\Unit\Services;

use App\Services\TemplateService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class TemplateServiceTest extends TestCase
{
    protected $templateService;
    protected $mockUrl = 'https://mock-supabase-url.com';
    protected $mockKey = 'mock-supabase-key';
    protected $mockServiceKey = 'mock-service-key';
    protected $userId = '123e4567-e89b-12d3-a456-426614174000';

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the Supabase config
        Config::set('supabase.url', $this->mockUrl);
        Config::set('supabase.key', $this->mockKey);
        Config::set('supabase.service_role', $this->mockServiceKey);

        $this->templateService = new TemplateService();
    }

    public function testGetTemplatesForUser()
    {
        // Mock response data
        $mockTemplates = [
            [
                'id' => '123e4567-e89b-12d3-a456-426614174001',
                'user_id' => $this->userId,
                'name' => 'Test Template 1',
                'description' => 'Test Description 1',
                'logo' => 'logo-123.png',
                'primary_color' => '#004cbf',
                'secondary_color' => '#0dff96',
                'content' => '<h1>Template 1</h1><p>Please leave a review</p>',
                'is_active' => true,
                'view_count' => 5,
                'submission_count' => 2,
                'created_at' => '2023-01-01T00:00:00Z',
                'updated_at' => '2023-01-01T00:00:00Z'
            ],
            [
                'id' => '123e4567-e89b-12d3-a456-426614174002',
                'user_id' => $this->userId,
                'name' => 'Test Template 2',
                'description' => 'Test Description 2',
                'logo' => 'logo-456.png',
                'primary_color' => '#ff0000',
                'secondary_color' => '#00ff00',
                'content' => '<h1>Template 2</h1><p>Please leave a review</p>',
                'is_active' => true,
                'view_count' => 10,
                'submission_count' => 5,
                'created_at' => '2023-01-02T00:00:00Z',
                'updated_at' => '2023-01-02T00:00:00Z'
            ]
        ];

        // Mock HTTP response
        Http::fake([
            "{$this->mockUrl}/rest/v1/templates*" => Http::response($mockTemplates, 200)
        ]);

        // Call the method
        $result = $this->templateService->getTemplatesForUser($this->userId, 10, 0);

        // Assert the result
        $this->assertEquals($mockTemplates, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) {
            return $request->url() == "{$this->mockUrl}/rest/v1/templates" &&
                   $request->method() == 'GET' &&
                   $request['user_id'] == 'eq.' . $this->userId &&
                   $request['limit'] == 10 &&
                   $request['offset'] == 0 &&
                   $request['order'] == 'created_at.desc';
        });
    }

    public function testCountTemplatesForUser()
    {
        // Mock HTTP response with Content-Range header
        Http::fake([
            "{$this->mockUrl}/rest/v1/templates*" => Http::response([], 200, [
                'Content-Range' => '0-0/8'
            ])
        ]);

        // Call the method
        $result = $this->templateService->countTemplatesForUser($this->userId);

        // Assert the result
        $this->assertEquals(8, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) {
            return $request->url() == "{$this->mockUrl}/rest/v1/templates" &&
                   $request->method() == 'GET' &&
                   $request['user_id'] == 'eq.' . $this->userId &&
                   $request['select'] == 'id' &&
                   $request['limit'] == 1;
        });
    }

    public function testCreateTemplate()
    {
        // Mock response data
        $name = 'New Template';
        $description = 'New Description';
        $primaryColor = '#004cbf';
        $secondaryColor = '#0dff96';
        $content = '<h1>New Template</h1><p>Please leave a review</p>';
        $logo = 'logo-789.png';
        
        $mockResponse = [
            'id' => '123e4567-e89b-12d3-a456-426614174003',
            'user_id' => $this->userId,
            'name' => $name,
            'description' => $description,
            'logo' => $logo,
            'primary_color' => $primaryColor,
            'secondary_color' => $secondaryColor,
            'content' => $content,
            'is_active' => true,
            'view_count' => 0,
            'submission_count' => 0,
            'created_at' => '2023-01-03T00:00:00Z',
            'updated_at' => '2023-01-03T00:00:00Z'
        ];

        // Mock HTTP response
        Http::fake([
            "{$this->mockUrl}/rest/v1/templates" => Http::response($mockResponse, 201)
        ]);

        // Call the method
        $result = $this->templateService->createTemplate(
            $this->userId,
            $name,
            $description,
            $primaryColor,
            $secondaryColor,
            $content,
            $logo
        );

        // Assert the result
        $this->assertEquals($mockResponse, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($name, $description, $primaryColor, $secondaryColor, $content, $logo) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/templates" &&
                   $request->method() == 'POST' &&
                   $data['user_id'] == $this->userId &&
                   $data['name'] == $name &&
                   $data['description'] == $description &&
                   $data['primary_color'] == $primaryColor &&
                   $data['secondary_color'] == $secondaryColor &&
                   $data['content'] == $content &&
                   $data['logo'] == $logo &&
                   $data['is_active'] === true &&
                   $data['view_count'] === 0 &&
                   $data['submission_count'] === 0;
        });
    }

    public function testUpdateTemplate()
    {
        // Mock response data
        $id = '123e4567-e89b-12d3-a456-426614174001';
        $updateData = [
            'name' => 'Updated Template',
            'description' => 'Updated Description',
            'is_active' => false
        ];
        $mockResponse = [
            'id' => $id,
            'user_id' => $this->userId,
            'name' => 'Updated Template',
            'description' => 'Updated Description',
            'logo' => 'logo-123.png',
            'primary_color' => '#004cbf',
            'secondary_color' => '#0dff96',
            'content' => '<h1>Template 1</h1><p>Please leave a review</p>',
            'is_active' => false,
            'view_count' => 5,
            'submission_count' => 2,
            'created_at' => '2023-01-01T00:00:00Z',
            'updated_at' => '2023-01-04T00:00:00Z'
        ];

        // Mock HTTP response
        Http::fake([
            "{$this->mockUrl}/rest/v1/templates*" => Http::response($mockResponse, 200)
        ]);

        // Call the method
        $result = $this->templateService->updateTemplate($id, $updateData);

        // Assert the result
        $this->assertEquals($mockResponse, $result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($id, $updateData) {
            return $request->url() == "{$this->mockUrl}/rest/v1/templates" &&
                   $request->method() == 'PATCH' &&
                   $request['id'] == 'eq.' . $id &&
                   json_decode($request->body(), true) == $updateData;
        });
    }

    public function testDeleteTemplate()
    {
        // Mock response data
        $id = '123e4567-e89b-12d3-a456-426614174001';

        // Mock HTTP response
        Http::fake([
            "{$this->mockUrl}/rest/v1/templates*" => Http::response(null, 204)
        ]);

        // Call the method
        $result = $this->templateService->deleteTemplate($id);

        // Assert the result
        $this->assertTrue($result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($id) {
            return $request->url() == "{$this->mockUrl}/rest/v1/templates" &&
                   $request->method() == 'DELETE' &&
                   $request['id'] == 'eq.' . $id;
        });
    }

    public function testIncrementViewCount()
    {
        // Mock response data
        $id = '123e4567-e89b-12d3-a456-426614174001';

        // Mock SQL query response
        Http::fake([
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::response(null, 200)
        ]);

        // Call the method
        $result = $this->templateService->incrementViewCount($id);

        // Assert the result
        $this->assertTrue($result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($id) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/rpc/execute_sql" &&
                   $request->method() == 'POST' &&
                   $data['params']['template_id'] == $id &&
                   strpos($data['query'], 'SELECT increment_template_view_count(:template_id)') !== false;
        });
    }

    public function testIncrementSubmissionCount()
    {
        // Mock response data
        $id = '123e4567-e89b-12d3-a456-426614174001';

        // Mock SQL query response
        Http::fake([
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => Http::response(null, 200)
        ]);

        // Call the method
        $result = $this->templateService->incrementSubmissionCount($id);

        // Assert the result
        $this->assertTrue($result);

        // Assert the request was made correctly
        Http::assertSent(function ($request) use ($id) {
            $data = json_decode($request->body(), true);
            return $request->url() == "{$this->mockUrl}/rest/v1/rpc/execute_sql" &&
                   $request->method() == 'POST' &&
                   $data['params']['template_id'] == $id &&
                   strpos($data['query'], 'SELECT increment_template_submission_count(:template_id)') !== false;
        });
    }

    public function testGetTemplateStats()
    {
        // Mock HTTP responses for count queries
        Http::fake([
            // Total count
            "{$this->mockUrl}/rest/v1/templates?select=id&limit=1&user_id=eq.{$this->userId}" => 
                Http::response([], 200, ['Content-Range' => '0-0/5']),
            
            // Active count
            "{$this->mockUrl}/rest/v1/templates?select=id&limit=1&user_id=eq.{$this->userId}&is_active=eq.true" => 
                Http::response([], 200, ['Content-Range' => '0-0/4']),
            
            // Get totals for views and submissions
            "{$this->mockUrl}/rest/v1/rpc/execute_sql" => 
                Http::response([
                    [
                        'total_views' => 50,
                        'total_submissions' => 20
                    ]
                ], 200)
        ]);

        // Call the method
        $result = $this->templateService->getTemplateStats($this->userId);

        // Assert the result
        $this->assertEquals([
            'total' => 5,
            'active' => 4,
            'inactive' => 1,
            'total_views' => 50,
            'total_submissions' => 20
        ], $result);
    }
}
