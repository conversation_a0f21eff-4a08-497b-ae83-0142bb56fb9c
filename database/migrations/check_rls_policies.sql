-- Check existing RLS policies for qr_codes table
SELECT *
FROM pg_policies
WHERE tablename = 'qr_codes';

-- Enable RLS on qr_codes table if not already enabled
ALTER TABLE qr_codes ENABLE ROW LEVEL SECURITY;

-- Create or replace policies for qr_codes table
-- Allow users to select their own QR codes
DROP POLICY IF EXISTS "Users can view their own QR codes" ON qr_codes;
CREATE POLICY "Users can view their own QR codes"
    ON qr_codes
    FOR SELECT
    USING (auth.uid() = user_id);

-- Allow users to insert their own QR codes
DROP POLICY IF EXISTS "Users can insert their own QR codes" ON qr_codes;
CREATE POLICY "Users can insert their own QR codes"
    ON qr_codes
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own QR codes
DROP POLICY IF EXISTS "Users can update their own QR codes" ON qr_codes;
CREATE POLICY "Users can update their own QR codes"
    ON qr_codes
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Allow users to delete their own QR codes
DROP POLICY IF EXISTS "Users can delete their own QR codes" ON qr_codes;
CREATE POLICY "Users can delete their own QR codes"
    ON qr_codes
    FOR DELETE
    USING (auth.uid() = user_id);
