-- Create the execute_sql function if it doesn't exist
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION execute_sql(query text, params jsonb DEFAULT '{}'::jsonb)
RETURNS SETOF json AS $$
DECLARE
    result json;
    sql text;
    param_names text[];
    param_values text[];
    i int;
BEGIN
    -- Extract parameter names and values
    SELECT array_agg(key), array_agg(value::text)
    INTO param_names, param_values
    FROM jsonb_each(params);
    
    -- Replace parameters in the query
    sql := query;
    IF param_names IS NOT NULL THEN
        FOR i IN 1..array_length(param_names, 1) LOOP
            sql := replace(sql, ':' || param_names[i], param_values[i]);
        END LOOP;
    END IF;
    
    -- Execute the query and return results
    FOR result IN EXECUTE sql LOOP
        RETURN NEXT result;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
