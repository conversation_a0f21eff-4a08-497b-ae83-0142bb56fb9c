-- This script adds a UUID column to the qr_codes table
-- Run this script directly in the Supabase SQL Editor

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add UUID column to qr_codes table if it doesn't exist
ALTER TABLE qr_codes ADD COLUMN IF NOT EXISTS uuid UUID DEFAULT uuid_generate_v4();

-- Create index on UUID for faster lookups
CREATE INDEX IF NOT EXISTS qr_codes_uuid_idx ON qr_codes(uuid);

-- Make sure all existing QR codes have a UUID
UPDATE qr_codes SET uuid = uuid_generate_v4() WHERE uuid IS NULL;

-- Make UUID NOT NULL after ensuring all rows have a value
ALTER TABLE qr_codes ALTER COLUMN uuid SET NOT NULL;

-- Verify the changes
SELECT id, uuid FROM qr_codes LIMIT 10;
