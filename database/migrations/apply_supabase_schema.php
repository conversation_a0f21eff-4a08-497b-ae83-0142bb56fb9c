<?php

/**
 * Supabase Schema Migration Script
 * 
 * This script applies the SQL schema to your Supabase project using the Supabase API.
 * It reads the SQL from the supabase_schema.sql and supabase_schema_update.sql files 
 * and executes them via the Supabase REST API.
 * 
 * It also sets up the storage bucket for QR codes using the SetupSupabaseStorage class.
 */

// Load environment variables from .env file
require_once __DIR__ . '/../../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
$dotenv->load();

// Supabase credentials
$supabaseUrl = $_ENV['SUPABASE_URL'];
$supabaseKey = $_ENV['SUPABASE_SERVICE_ROLE']; // Use service role key for admin operations

// Read the SQL schema files
$sqlSchema = file_get_contents(__DIR__ . '/supabase_schema.sql');

// Read the schema update file if it exists
$schemaUpdatePath = __DIR__ . '/supabase_schema_update.sql';
if (file_exists($schemaUpdatePath)) {
    $sqlSchemaUpdate = file_get_contents($schemaUpdatePath);
    $sqlSchema .= "\n" . $sqlSchemaUpdate;
}

// Split the SQL into separate statements
$statements = explode(';', $sqlSchema);

// Initialize cURL
$ch = curl_init();

// Set common cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $supabaseKey,
    'apikey: ' . $supabaseKey
]);

// Execute each SQL statement
$successCount = 0;
$errorCount = 0;
$errors = [];

echo "Starting Supabase schema migration...\n";

foreach ($statements as $statement) {
    // Skip empty statements
    $statement = trim($statement);
    if (empty($statement)) {
        continue;
    }
    
    // Prepare the request data for SQL Editor API
    $data = json_encode([
        'query' => $statement,
        'params' => []
    ]);
    
    // Set cURL options for this request
    // Use the SQL Editor API endpoint instead of execute_sql function
    curl_setopt($ch, CURLOPT_URL, $supabaseUrl . '/rest/v1/sql');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    
    // Execute the request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // Check if the request was successful
    if ($httpCode >= 200 && $httpCode < 300) {
        echo "✓ Successfully executed statement\n";
        $successCount++;
    } else {
        echo "✗ Error executing statement: " . $httpCode . "\n";
        $errorCount++;
        $errors[] = [
            'statement' => $statement,
            'response' => $response,
            'httpCode' => $httpCode
        ];
    }
}

// Close cURL
curl_close($ch);

// Print summary
echo "\nMigration completed with {$successCount} successful statements and {$errorCount} errors.\n";

// Print errors if any
if ($errorCount > 0) {
    echo "\nErrors:\n";
    foreach ($errors as $index => $error) {
        echo "Error " . ($index + 1) . ":\n";
        echo "Statement: " . $error['statement'] . "\n";
        echo "Response: " . $error['response'] . "\n";
        echo "HTTP Code: " . $error['httpCode'] . "\n\n";
    }
}

echo "\nNote: Some errors may be expected if tables or constraints already exist.\n";
echo "You can also manually execute the SQL schema by copying the contents of supabase_schema.sql\n";
echo "and pasting it into the Supabase SQL Editor in the dashboard.\n";

// Run the storage setup
echo "\nSetting up Supabase storage for QR codes...\n";
require_once __DIR__ . '/setup_supabase_storage.php';

use Database\Migrations\SetupSupabaseStorage;

$storageResult = SetupSupabaseStorage::run();
if ($storageResult) {
    echo "✓ Successfully set up Supabase storage for QR codes\n";
} else {
    echo "✗ Error setting up Supabase storage for QR codes\n";
    echo "You may need to manually create a 'qr-codes' bucket in the Supabase dashboard\n";
    echo "and set it to public with appropriate policies.\n";
}
