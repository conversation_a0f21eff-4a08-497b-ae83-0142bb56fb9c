<?php

namespace Database\Migrations;

// We can't use Laravel facades here since we're running this script directly
// So we'll use cURL instead

/**
 * Setup Supabase storage for QR codes
 * 
 * This script creates a storage bucket for QR codes and sets up the appropriate policies
 * Run this script after applying the database migrations
 */
class SetupSupabaseStorage
{
    /**
     * Run the storage setup
     */
    public static function run()
    {
        global $supabaseUrl, $supabaseKey;
        
        if (!$supabaseUrl || !$supabaseKey) {
            echo "Error: Supabase URL or service role key not configured\n";
            return false;
        }
        
        // Create the bucket if it doesn't exist
        $ch = curl_init();
        
        // Set common cURL options
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $supabaseKey,
            'apikey: ' . $supabaseKey
        ]);
        
        // Create bucket request
        curl_setopt($ch, CURLOPT_URL, $supabaseUrl . '/storage/v1/buckets');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'id' => 'qr-codes',
            'name' => 'QR Codes',
            'public' => true
        ]));
        
        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            echo "✓ QR codes bucket created successfully\n";
        } elseif ($httpCode === 409) {
            echo "✓ QR codes bucket already exists\n";
        } else {
            echo "✗ Failed to create QR codes bucket: " . $httpCode . "\n";
            echo "Response: " . $response . "\n";
            curl_close($ch);
            return false;
        }
        
        // Set up public read policy for the bucket
        curl_setopt($ch, CURLOPT_URL, $supabaseUrl . '/storage/v1/buckets/qr-codes/policies');
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'name' => 'Public Read Policy',
            'definition' => [
                'type' => 'READ',
                'permissions' => ['anon', 'authenticated']
            ]
        ]));
        
        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // Close cURL
        curl_close($ch);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            echo "✓ QR codes bucket policy created successfully\n";
            return true;
        } else {
            echo "✗ Failed to create QR codes bucket policy: " . $httpCode . "\n";
            echo "Response: " . $response . "\n";
            return false;
        }
    }
}
