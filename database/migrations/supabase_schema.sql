-- RatingShield QR System Database Schema
-- This script creates the necessary tables for the RatingShield QR system in Supabase

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    subscription_plan VARCHAR(50) DEFAULT 'free',
    subscription_status VARCHAR(50) DEFAULT 'active',
    subscription_ends_at TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create qr_codes table
CREATE TABLE IF NOT EXISTS public.qr_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    code_data TEXT NOT NULL,
    code_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    scan_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create reviews table
CREATE TABLE IF NOT EXISTS public.reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    qr_code_id UUID NOT NULL REFERENCES public.qr_codes(id) ON DELETE CASCADE,
    template_id UUID,
    customer_name VARCHAR(255),
    customer_email VARCHAR(255),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    additional_feedback TEXT,
    vendor_response TEXT,
    is_published_to_google BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create templates table
CREATE TABLE IF NOT EXISTS public.templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    logo VARCHAR(255),
    primary_color VARCHAR(7) DEFAULT '#004cbf',
    secondary_color VARCHAR(7) DEFAULT '#0dff96',
    content TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    view_count INTEGER DEFAULT 0,
    submission_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key to reviews table for template_id
ALTER TABLE public.reviews
ADD CONSTRAINT fk_reviews_template
FOREIGN KEY (template_id) REFERENCES public.templates(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX idx_qr_codes_user_id ON public.qr_codes(user_id);
CREATE INDEX idx_reviews_qr_code_id ON public.reviews(qr_code_id);
CREATE INDEX idx_reviews_template_id ON public.reviews(template_id);
CREATE INDEX idx_templates_user_id ON public.templates(user_id);
CREATE INDEX idx_reviews_rating ON public.reviews(rating);
CREATE INDEX idx_reviews_created_at ON public.reviews(created_at);

-- Create RLS (Row Level Security) policies
-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
CREATE POLICY users_select_policy ON public.users
    FOR SELECT USING (auth.uid() = id);
    
CREATE POLICY users_update_policy ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Create policies for qr_codes table
CREATE POLICY qr_codes_select_policy ON public.qr_codes
    FOR SELECT USING (auth.uid() = user_id);
    
CREATE POLICY qr_codes_insert_policy ON public.qr_codes
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
CREATE POLICY qr_codes_update_policy ON public.qr_codes
    FOR UPDATE USING (auth.uid() = user_id);
    
CREATE POLICY qr_codes_delete_policy ON public.qr_codes
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for reviews table
CREATE POLICY reviews_select_policy ON public.reviews
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.qr_codes
            WHERE qr_codes.id = reviews.qr_code_id
            AND qr_codes.user_id = auth.uid()
        )
    );
    
CREATE POLICY reviews_insert_policy ON public.reviews
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.qr_codes
            WHERE qr_codes.id = reviews.qr_code_id
            AND qr_codes.user_id = auth.uid()
        )
    );
    
CREATE POLICY reviews_update_policy ON public.reviews
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.qr_codes
            WHERE qr_codes.id = reviews.qr_code_id
            AND qr_codes.user_id = auth.uid()
        )
    );
    
CREATE POLICY reviews_delete_policy ON public.reviews
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.qr_codes
            WHERE qr_codes.id = reviews.qr_code_id
            AND qr_codes.user_id = auth.uid()
        )
    );

-- Create policies for templates table
CREATE POLICY templates_select_policy ON public.templates
    FOR SELECT USING (auth.uid() = user_id);
    
CREATE POLICY templates_insert_policy ON public.templates
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
CREATE POLICY templates_update_policy ON public.templates
    FOR UPDATE USING (auth.uid() = user_id);
    
CREATE POLICY templates_delete_policy ON public.templates
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updated_at timestamp
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_qr_codes_updated_at
BEFORE UPDATE ON public.qr_codes
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at
BEFORE UPDATE ON public.reviews
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at
BEFORE UPDATE ON public.templates
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to increment scan_count when a QR code is scanned
CREATE OR REPLACE FUNCTION increment_qr_code_scan_count()
RETURNS TRIGGER AS $$
BEGIN
   UPDATE public.qr_codes
   SET scan_count = scan_count + 1
   WHERE id = NEW.qr_code_id;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to increment scan_count when a review is created
CREATE TRIGGER increment_scan_count_on_review
AFTER INSERT ON public.reviews
FOR EACH ROW EXECUTE FUNCTION increment_qr_code_scan_count();

-- Create function to increment view_count and submission_count for templates
CREATE OR REPLACE FUNCTION increment_template_view_count(template_id UUID)
RETURNS VOID AS $$
BEGIN
   UPDATE public.templates
   SET view_count = view_count + 1
   WHERE id = template_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_template_submission_count(template_id UUID)
RETURNS VOID AS $$
BEGIN
   UPDATE public.templates
   SET submission_count = submission_count + 1
   WHERE id = template_id;
END;
$$ LANGUAGE plpgsql;
