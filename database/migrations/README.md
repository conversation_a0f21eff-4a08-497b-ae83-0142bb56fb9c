# Supabase Database Schema for RatingShield QR System

This directory contains the SQL schema and migration scripts for setting up the RatingShield QR System database in Supabase.

## Schema Overview

The database schema includes the following tables:

1. **users** - Extends the Supabase auth.users table with additional user information
2. **qr_codes** - Stores QR code information generated by vendors
3. **reviews** - Stores customer reviews submitted through QR codes
4. **templates** - Stores email templates for review collection

## Applying the Schema to Supabase

### Option 1: Using the Supabase SQL Editor (Recommended)

1. Log in to your [Supabase Dashboard](https://app.supabase.com)
2. Select your project
3. Go to the SQL Editor tab
4. Create a new query
5. Copy the contents of `supabase_schema.sql` and paste it into the SQL Editor
6. Click "Run" to execute the SQL and create the schema

### Option 2: Using the PHP Migration Script

You can also apply the schema programmatically using the provided PHP script:

1. Make sure your `.env` file contains the correct Supabase credentials:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE=your_service_role_key
   ```

2. Run the migration script:
   ```bash
   php database/migrations/apply_supabase_schema.php
   ```

## Row Level Security (RLS)

The schema includes Row Level Security policies to ensure that users can only access their own data. These policies are automatically applied when you run the schema migration.

## Database Triggers

The schema includes several triggers:

1. Automatic updating of `updated_at` timestamps
2. Incrementing QR code scan counts when reviews are submitted
3. Functions for tracking template views and submissions

## Notes

- If you're applying the schema to an existing database, some statements might fail if tables or constraints already exist. This is expected behavior.
- The schema uses UUIDs for primary keys to be compatible with Supabase's authentication system.
- Make sure your Supabase project has the UUID extension enabled (included in the migration script).

## Troubleshooting

If you encounter issues with the migration:

1. Check that your Supabase credentials are correct
2. Try applying the schema statements one by one in the SQL Editor
3. Check the Supabase logs for more detailed error messages

For more information on Supabase database management, refer to the [Supabase Documentation](https://supabase.com/docs/guides/database).
