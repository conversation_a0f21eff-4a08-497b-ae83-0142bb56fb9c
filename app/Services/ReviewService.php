<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ReviewService extends SupabaseService
{
    public function __construct()
    {
        parent::__construct();
        $this->setTable('reviews');
    }

    /**
     * Get reviews for a specific user's QR codes
     *
     * @param string $userId
     * @param array $filters
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getReviewsForUser(string $userId, array $filters = [], int $limit = 10, int $offset = 0)
    {
        // We need to join with qr_codes to filter by user_id
        $query = "
            SELECT r.* 
            FROM reviews r
            JOIN qr_codes q ON r.qr_code_id = q.id
            WHERE q.user_id = :user_id
        ";
        
        $params = [
            'user_id' => $userId
        ];
        
        // Add rating filter if provided
        if (isset($filters['rating']) && $filters['rating'] !== '') {
            $query .= " AND r.rating = :rating";
            $params['rating'] = $filters['rating'];
        }
        
        // Add date range filter if provided
        if (isset($filters['start_date']) && isset($filters['end_date']) && 
            $filters['start_date'] !== '' && $filters['end_date'] !== '') {
            $query .= " AND r.created_at BETWEEN :start_date AND :end_date";
            $params['start_date'] = $filters['start_date'];
            $params['end_date'] = $filters['end_date'];
        }
        
        // Add ordering
        $query .= " ORDER BY r.created_at DESC";
        
        // Add pagination
        $query .= " LIMIT :limit OFFSET :offset";
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        try {
            $result = $this->query($query, $params);
            return $result ?? [];
        } catch (\Exception $e) {
            Log::error('Failed to get reviews for user', [
                'error' => $e->getMessage(),
                'user_id' => $userId,
                'filters' => $filters
            ]);
            
            return [];
        }
    }

    /**
     * Count reviews for a specific user's QR codes
     *
     * @param string $userId
     * @param array $filters
     * @return int
     */
    public function countReviewsForUser(string $userId, array $filters = [])
    {
        // We need to join with qr_codes to filter by user_id
        $query = "
            SELECT COUNT(*) as count
            FROM reviews r
            JOIN qr_codes q ON r.qr_code_id = q.id
            WHERE q.user_id = :user_id
        ";
        
        $params = [
            'user_id' => $userId
        ];
        
        // Add rating filter if provided
        if (isset($filters['rating']) && $filters['rating'] !== '') {
            $query .= " AND r.rating = :rating";
            $params['rating'] = $filters['rating'];
        }
        
        // Add date range filter if provided
        if (isset($filters['start_date']) && isset($filters['end_date']) && 
            $filters['start_date'] !== '' && $filters['end_date'] !== '') {
            $query .= " AND r.created_at BETWEEN :start_date AND :end_date";
            $params['start_date'] = $filters['start_date'];
            $params['end_date'] = $filters['end_date'];
        }
        
        try {
            $result = $this->query($query, $params);
            return isset($result[0]['count']) ? (int) $result[0]['count'] : 0;
        } catch (\Exception $e) {
            Log::error('Failed to count reviews for user', [
                'error' => $e->getMessage(),
                'user_id' => $userId,
                'filters' => $filters
            ]);
            
            return 0;
        }
    }

    /**
     * Get recent reviews for a specific user's QR codes
     *
     * @param string $userId
     * @param int $limit
     * @return array
     */
    public function getRecentReviewsForUser(string $userId, int $limit = 5)
    {
        return $this->getReviewsForUser($userId, [], $limit, 0);
    }
    
    /**
     * Get a review by ID
     *
     * @param string $id
     * @return array|null
     */
    public function getReviewById(string $id)
    {
        try {
            return $this->getById($id);
        } catch (\Exception $e) {
            Log::error('Failed to get review by ID', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return null;
        }
    }
    
    /**
     * Create a new review
     *
     * @param array $data
     * @return array|null
     */
    public function createReview(array $data)
    {
        try {
            return $this->create($data);
        } catch (\Exception $e) {
            Log::error('Failed to create review', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            return null;
        }
    }
    
    /**
     * Update a review
     *
     * @param string $id
     * @param array $data
     * @return array|null
     */
    public function updateReview(string $id, array $data)
    {
        try {
            return $this->update($id, $data);
        } catch (\Exception $e) {
            Log::error('Failed to update review', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            
            return null;
        }
    }

    /**
     * Add a vendor response to a review
     *
     * @param string $reviewId
     * @param string $response
     * @return array|null
     */
    public function addResponseToReview(string $reviewId, string $response)
    {
        try {
            return $this->update($reviewId, [
                'vendor_response' => $response
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to add response to review', [
                'error' => $e->getMessage(),
                'review_id' => $reviewId,
                'response' => $response
            ]);
            
            return null;
        }
    }

    /**
     * Get review statistics for a user
     *
     * @param string $userId
     * @return array
     */
    public function getReviewStats(string $userId)
    {
        $stats = [
            'total' => 0,
            'average_rating' => 0,
            'rating_distribution' => [
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0
            ],
            'responded' => 0,
            'published_to_google' => 0
        ];
        
        try {
            // Get total count
            $stats['total'] = $this->countReviewsForUser($userId);
            
            // Get rating distribution and calculate average
            $query = "
                SELECT r.rating, COUNT(*) as count
                FROM reviews r
                JOIN qr_codes q ON r.qr_code_id = q.id
                WHERE q.user_id = :user_id
                GROUP BY r.rating
            ";
            
            $ratingDistribution = $this->query($query, ['user_id' => $userId]);
            
            if ($ratingDistribution) {
                $totalRatings = 0;
                $sumRatings = 0;
                
                foreach ($ratingDistribution as $row) {
                    $rating = (int) $row['rating'];
                    $count = (int) $row['count'];
                    
                    $stats['rating_distribution'][$rating] = $count;
                    $totalRatings += $count;
                    $sumRatings += ($rating * $count);
                }
                
                if ($totalRatings > 0) {
                    $stats['average_rating'] = $sumRatings / $totalRatings;
                }
            }
            
            // Get count of reviews with vendor responses
            $query = "
                SELECT COUNT(*) as count
                FROM reviews r
                JOIN qr_codes q ON r.qr_code_id = q.id
                WHERE q.user_id = :user_id
                AND r.vendor_response IS NOT NULL
            ";
            
            $respondedResult = $this->query($query, ['user_id' => $userId]);
            $stats['responded'] = isset($respondedResult[0]['count']) ? (int) $respondedResult[0]['count'] : 0;
            
            // Get count of reviews published to Google
            $query = "
                SELECT COUNT(*) as count
                FROM reviews r
                JOIN qr_codes q ON r.qr_code_id = q.id
                WHERE q.user_id = :user_id
                AND r.is_published_to_google = true
            ";
            
            $publishedResult = $this->query($query, ['user_id' => $userId]);
            $stats['published_to_google'] = isset($publishedResult[0]['count']) ? (int) $publishedResult[0]['count'] : 0;
            
            return $stats;
        } catch (\Exception $e) {
            Log::error('Failed to get review stats', [
                'error' => $e->getMessage(),
                'user_id' => $userId
            ]);
            
            return $stats;
        }
    }

    /**
     * Get monthly review counts for a user
     *
     * @param string $userId
     * @param int $months
     * @return array
     */
    public function getMonthlyReviewCounts(string $userId, int $months = 6)
    {
        $result = [];
        
        try {
            $query = "
                SELECT 
                    DATE_TRUNC('month', r.created_at) as month,
                    COUNT(*) as count
                FROM reviews r
                JOIN qr_codes q ON r.qr_code_id = q.id
                WHERE q.user_id = :user_id
                AND r.created_at >= NOW() - INTERVAL ':months months'
                GROUP BY DATE_TRUNC('month', r.created_at)
                ORDER BY month DESC
                LIMIT :months
            ";
            
            $monthlyData = $this->query($query, [
                'user_id' => $userId,
                'months' => $months
            ]);
            
            if ($monthlyData) {
                foreach ($monthlyData as $row) {
                    $month = date('M Y', strtotime($row['month']));
                    $count = (int) $row['count'];
                    
                    $result[] = [
                        'month' => $month,
                        'count' => $count
                    ];
                }
            }
            
            // Fill in missing months with zero counts
            $existingMonths = array_column($result, 'month');
            
            for ($i = 0; $i < $months; $i++) {
                $monthDate = date('M Y', strtotime("-$i months"));
                
                if (!in_array($monthDate, $existingMonths)) {
                    $result[] = [
                        'month' => $monthDate,
                        'count' => 0
                    ];
                }
            }
            
            // Sort by month
            usort($result, function($a, $b) {
                return strtotime($a['month']) - strtotime($b['month']);
            });
            
            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to get monthly review counts', [
                'error' => $e->getMessage(),
                'user_id' => $userId,
                'months' => $months
            ]);
            
            // Return empty data for the last $months months
            for ($i = $months - 1; $i >= 0; $i--) {
                $monthDate = date('M Y', strtotime("-$i months"));
                $result[] = [
                    'month' => $monthDate,
                    'count' => 0
                ];
            }
            
            return $result;
        }
    }
}
