<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TemplateService extends SupabaseService
{
    public function __construct()
    {
        parent::__construct();
        $this->setTable('templates');
    }

    /**
     * Get templates for a specific user
     *
     * @param string $userId
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getTemplatesForUser(string $userId, int $limit = 10, int $offset = 0)
    {
        return $this->getAll(['*'], ['user_id' => $userId], [
            'order' => 'created_at.desc',
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * Count templates for a specific user
     *
     * @param string $userId
     * @return int
     */
    public function countTemplatesForUser(string $userId)
    {
        return $this->count(['user_id' => $userId]);
    }

    /**
     * Create a new template
     *
     * @param string $userId
     * @param string $name
     * @param string $description
     * @param string $primaryColor
     * @param string $secondaryColor
     * @param string $content
     * @param string|null $logo
     * @return array|null
     */
    public function createTemplate(
        string $userId,
        string $name,
        string $description,
        string $primaryColor,
        string $secondaryColor,
        string $content,
        string $logo = null
    ) {
        $data = [
            'user_id' => $userId,
            'name' => $name,
            'description' => $description,
            'primary_color' => $primaryColor,
            'secondary_color' => $secondaryColor,
            'content' => $content,
            'logo' => $logo,
            'is_active' => true,
            'view_count' => 0,
            'submission_count' => 0
        ];
        
        try {
            return $this->create($data);
        } catch (\Exception $e) {
            Log::error('Failed to create template', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            return null;
        }
    }

    /**
     * Update a template
     *
     * @param string $id
     * @param array $data
     * @return array|null
     */
    public function updateTemplate(string $id, array $data)
    {
        try {
            return $this->update($id, $data);
        } catch (\Exception $e) {
            Log::error('Failed to update template', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            
            return null;
        }
    }

    /**
     * Delete a template
     *
     * @param string $id
     * @return bool
     */
    public function deleteTemplate(string $id)
    {
        try {
            return $this->delete($id);
        } catch (\Exception $e) {
            Log::error('Failed to delete template', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return false;
        }
    }

    /**
     * Increment the view count for a template
     *
     * @param string $id
     * @return bool
     */
    public function incrementViewCount(string $id)
    {
        try {
            $query = "
                SELECT increment_template_view_count(:template_id)
            ";
            
            $this->query($query, ['template_id' => $id]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to increment template view count', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return false;
        }
    }

    /**
     * Increment the submission count for a template
     *
     * @param string $id
     * @return bool
     */
    public function incrementSubmissionCount(string $id)
    {
        try {
            $query = "
                SELECT increment_template_submission_count(:template_id)
            ";
            
            $this->query($query, ['template_id' => $id]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to increment template submission count', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return false;
        }
    }

    /**
     * Get template statistics for a user
     *
     * @param string $userId
     * @return array
     */
    public function getTemplateStats(string $userId)
    {
        $stats = [
            'total' => 0,
            'active' => 0,
            'inactive' => 0,
            'total_views' => 0,
            'total_submissions' => 0
        ];
        
        try {
            // Get total count
            $stats['total'] = $this->count(['user_id' => $userId]);
            
            // Get active count
            $stats['active'] = $this->count([
                'user_id' => $userId,
                'is_active' => true
            ]);
            
            // Get inactive count
            $stats['inactive'] = $stats['total'] - $stats['active'];
            
            // Get total views and submissions
            $query = "
                SELECT 
                    SUM(view_count) as total_views,
                    SUM(submission_count) as total_submissions
                FROM templates
                WHERE user_id = :user_id
            ";
            
            $result = $this->query($query, ['user_id' => $userId]);
            
            if ($result && isset($result[0])) {
                $stats['total_views'] = (int) $result[0]['total_views'] ?? 0;
                $stats['total_submissions'] = (int) $result[0]['total_submissions'] ?? 0;
            }
            
            return $stats;
        } catch (\Exception $e) {
            Log::error('Failed to get template stats', [
                'error' => $e->getMessage(),
                'user_id' => $userId
            ]);
            
            return $stats;
        }
    }
}
