<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode as QrGenerator;

class QrCodeService extends SupabaseService
{
    public function __construct()
    {
        parent::__construct();
        $this->setTable('qr_codes');
    }

    /**
     * Get QR codes for a specific user
     *
     * @param string $userId
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getQrCodesForUser(string $userId, int $limit = 10, int $offset = 0)
    {
        // Use service role key to bypass RLS policies
        return $this->getAll(['*'], ['user_id' => $userId], [
            'order' => 'created_at.desc',
            'limit' => $limit,
            'offset' => $offset
        ], true);
    }

    /**
     * Count QR codes for a specific user
     *
     * @param string $userId
     * @return int
     */
    public function countQrCodesForUser(string $userId)
    {
        // Use service role key to bypass RLS policies
        return $this->count(['user_id' => $userId], true);
    }

    /**
     * Create a new QR code
     *
     * @param string $userId
     * @param string $name
     * @param string $description
     * @return array|null
     */
    public function createQrCode(string $userId, string $name, string $description = null)
    {
        // Generate a unique ID for the QR code
        $uniqueId = Str::uuid()->toString();
        
        // Generate QR code data (URL for reviews)
        $codeData = url('/review/' . $uniqueId);
        
        try {
            // Generate QR code as SVG (doesn't require Imagick)
            $qrCode = QrGenerator::size(300)
                ->errorCorrection('H')
                ->generate($codeData);
            
            // Save QR code image to storage
            $imageName = 'qr-' . $uniqueId . '.svg';
            $imagePath = 'qr-codes/' . $imageName;
            
            // Ensure the directory exists in public storage
            if (!Storage::disk('public')->exists('qr-codes')) {
                Storage::disk('public')->makeDirectory('qr-codes');
            }
            
            // Save the QR code image to public storage
            Storage::disk('public')->put($imagePath, $qrCode);
            
            // Prepare data for insertion
            $data = [
                'user_id' => $userId,
                'name' => $name,
                'description' => $description,
                'code_data' => $codeData,
                'code_image' => $imageName,
                'uuid' => $uniqueId,
                'is_active' => true,
                'scan_count' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ];
            
            // Use the create method with service role key to bypass RLS policies
            return $this->create($data, true);
        } catch (\Exception $e) {
            Log::error('Failed to create QR code', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return null;
        }
    }

    /**
     * Update a QR code
     *
     * @param string $id
     * @param array $data
     * @return array|null
     */
    public function updateQrCode(string $id, array $data)
    {
        try {
            return $this->update($id, $data);
        } catch (\Exception $e) {
            Log::error('Failed to update QR code', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            
            return null;
        }
    }

    /**
     * Delete a QR code
     *
     * @param string $id
     * @return bool
     */
    public function deleteQrCode(string $id)
    {
        try {
            return $this->delete($id);
        } catch (\Exception $e) {
            Log::error('Failed to delete QR code', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return false;
        }
    }

    /**
     * Get a QR code by UUID
     *
     * @param string $uuid
     * @return array|null
     */
    public function getQrCodeByUuid(string $uuid)
    {
        try {
            // Use service role key to bypass RLS policies
            $result = $this->getAll(['*'], ['uuid' => $uuid], [], true);
            
            if (empty($result)) {
                return null;
            }
            
            return $result[0];
        } catch (\Exception $e) {
            Log::error('Failed to get QR code by UUID', [
                'error' => $e->getMessage(),
                'uuid' => $uuid
            ]);
            
            return null;
        }
    }
    
    /**
     * Increment the scan count for a QR code
     *
     * @param string $id
     * @return bool
     */
    public function incrementScanCount(string $id)
    {
        try {
            // Get current QR code
            $qrCode = $this->getById($id);
            
            if (!$qrCode) {
                return false;
            }
            
            // Increment scan count
            $scanCount = $qrCode['scan_count'] + 1;
            
            // Update QR code
            $this->update($id, ['scan_count' => $scanCount]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to increment scan count', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return false;
        }
    }

    /**
     * Get QR code statistics for a user
     *
     * @param string $userId
     * @return array
     */
    public function getQrCodeStats(string $userId)
    {
        $stats = [
            'total' => 0,
            'active' => 0,
            'inactive' => 0,
            'total_scans' => 0
        ];
        
        try {
            // Get total count
            $stats['total'] = $this->count(['user_id' => $userId]);
            
            // Get active count
            $stats['active'] = $this->count([
                'user_id' => $userId,
                'is_active' => true
            ]);
            
            // Get inactive count
            $stats['inactive'] = $stats['total'] - $stats['active'];
            
            // Get total scans
            $qrCodes = $this->getAll(['scan_count'], ['user_id' => $userId]);
            $stats['total_scans'] = array_sum(array_column($qrCodes, 'scan_count'));
            
            return $stats;
        } catch (\Exception $e) {
            Log::error('Failed to get QR code stats', [
                'error' => $e->getMessage(),
                'user_id' => $userId
            ]);
            
            return $stats;
        }
    }
}
