<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SupabaseService
{
    protected $supabaseUrl;
    protected $supabaseKey;
    protected $supabaseServiceKey;
    protected $headers;
    protected $serviceHeaders;
    protected $table;

    public function __construct()
    {
        $this->supabaseUrl = config('supabase.url');
        $this->supabaseKey = config('supabase.key');
        $this->supabaseServiceKey = config('supabase.service_role');
        
        $this->headers = [
            'apikey' => $this->supabaseKey,
            'Authorization' => 'Bearer ' . $this->supabaseKey,
            'Content-Type' => 'application/json',
            'Prefer' => 'return=representation'
        ];
        
        $this->serviceHeaders = [
            'apikey' => $this->supabaseServiceKey,
            'Authorization' => 'Bearer ' . $this->supabaseServiceKey,
            'Content-Type' => 'application/json',
            'Prefer' => 'return=representation'
        ];
    }

    /**
     * Set the table name for the service
     *
     * @param string $table
     * @return $this
     */
    public function setTable(string $table)
    {
        $this->table = $table;
        return $this;
    }

    /**
     * Get all records from the table
     *
     * @param array $select Columns to select
     * @param array $filters Query filters
     * @param array $options Additional options (order, limit, offset)
     * @param bool $useServiceRole Whether to use service role key
     * @return array
     */
    public function getAll(array $select = ['*'], array $filters = [], array $options = [], bool $useServiceRole = false)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        $query = [];
        
        // Add select columns
        if ($select !== ['*']) {
            $query['select'] = implode(',', $select);
        }
        
        // Add filters
        foreach ($filters as $column => $value) {
            if (is_array($value) && isset($value['operator'])) {
                $query[$column] = $value['operator'] . '.' . $value['value'];
            } else {
                $query[$column] = 'eq.' . $value;
            }
        }
        
        // Add ordering
        if (isset($options['order'])) {
            $query['order'] = $options['order'];
        }
        
        // Add pagination
        if (isset($options['limit'])) {
            $query['limit'] = $options['limit'];
        }
        
        if (isset($options['offset'])) {
            $query['offset'] = $options['offset'];
        }
        
        try {
            $response = Http::withHeaders($headers)
                ->get($this->supabaseUrl . '/rest/v1/' . $this->table, $query);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Supabase getAll error', [
                'table' => $this->table,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return [];
        } catch (\Exception $e) {
            Log::error('Supabase getAll exception', [
                'table' => $this->table,
                'message' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Get a record by ID
     *
     * @param string $id
     * @param array $select Columns to select
     * @param bool $useServiceRole Whether to use service role key
     * @return array|null
     */
    public function getById(string $id, array $select = ['*'], bool $useServiceRole = false)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        $query = [];
        
        // Add select columns
        if ($select !== ['*']) {
            $query['select'] = implode(',', $select);
        }
        
        try {
            $response = Http::withHeaders($headers)
                ->get($this->supabaseUrl . '/rest/v1/' . $this->table, array_merge($query, [
                    'id' => 'eq.' . $id,
                    'limit' => 1
                ]));
            
            if ($response->successful()) {
                $data = $response->json();
                return !empty($data) ? $data[0] : null;
            }
            
            Log::error('Supabase getById error', [
                'table' => $this->table,
                'id' => $id,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('Supabase getById exception', [
                'table' => $this->table,
                'id' => $id,
                'message' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Create a new record
     *
     * @param array $data
     * @param bool $useServiceRole Whether to use service role key
     * @return array|null
     */
    public function create(array $data, bool $useServiceRole = false)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        
        try {
            $response = Http::withHeaders($headers)
                ->post($this->supabaseUrl . '/rest/v1/' . $this->table, $data);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Supabase create error', [
                'table' => $this->table,
                'data' => $data,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('Supabase create exception', [
                'table' => $this->table,
                'data' => $data,
                'message' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Update a record
     *
     * @param string $id
     * @param array $data
     * @param bool $useServiceRole Whether to use service role key
     * @return array|null
     */
    public function update(string $id, array $data, bool $useServiceRole = false)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        
        try {
            $response = Http::withHeaders($headers)
                ->patch($this->supabaseUrl . '/rest/v1/' . $this->table, $data, [
                    'id' => 'eq.' . $id
                ]);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Supabase update error', [
                'table' => $this->table,
                'id' => $id,
                'data' => $data,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('Supabase update exception', [
                'table' => $this->table,
                'id' => $id,
                'data' => $data,
                'message' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Delete a record
     *
     * @param string $id
     * @param bool $useServiceRole Whether to use service role key
     * @return bool
     */
    public function delete(string $id, bool $useServiceRole = false)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        
        try {
            $response = Http::withHeaders($headers)
                ->delete($this->supabaseUrl . '/rest/v1/' . $this->table, [
                    'id' => 'eq.' . $id
                ]);
            
            if ($response->successful()) {
                return true;
            }
            
            Log::error('Supabase delete error', [
                'table' => $this->table,
                'id' => $id,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return false;
        } catch (\Exception $e) {
            Log::error('Supabase delete exception', [
                'table' => $this->table,
                'id' => $id,
                'message' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Count records in the table
     *
     * @param array $filters Query filters
     * @param bool $useServiceRole Whether to use service role key
     * @return int
     */
    public function count(array $filters = [], bool $useServiceRole = false)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        $headers['Prefer'] = 'count=exact';
        
        $query = [];
        
        // Add filters
        foreach ($filters as $column => $value) {
            if (is_array($value) && isset($value['operator'])) {
                $query[$column] = $value['operator'] . '.' . $value['value'];
            } else {
                $query[$column] = 'eq.' . $value;
            }
        }
        
        try {
            $response = Http::withHeaders($headers)
                ->get($this->supabaseUrl . '/rest/v1/' . $this->table, array_merge($query, [
                    'select' => 'id',
                    'limit' => 1
                ]));
            
            if ($response->successful()) {
                return (int) $response->header('Content-Range', '0-0/0');
            }
            
            Log::error('Supabase count error', [
                'table' => $this->table,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return 0;
        } catch (\Exception $e) {
            Log::error('Supabase count exception', [
                'table' => $this->table,
                'message' => $e->getMessage()
            ]);
            
            return 0;
        }
    }

    /**
     * Execute a raw SQL query
     *
     * @param string $query
     * @param array $params
     * @param bool $useServiceRole Whether to use service role key
     * @return array|null
     */
    public function query(string $query, array $params = [], bool $useServiceRole = true)
    {
        $headers = $useServiceRole ? $this->serviceHeaders : $this->headers;
        
        try {
            $response = Http::withHeaders($headers)
                ->post($this->supabaseUrl . '/rest/v1/rpc/execute_sql', [
                    'query' => $query,
                    'params' => $params
                ]);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Supabase query error', [
                'query' => $query,
                'params' => $params,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('Supabase query exception', [
                'query' => $query,
                'params' => $params,
                'message' => $e->getMessage()
            ]);
            
            return null;
        }
    }
}
