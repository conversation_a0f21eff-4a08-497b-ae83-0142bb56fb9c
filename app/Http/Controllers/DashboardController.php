<?php

namespace App\Http\Controllers;

use App\Services\QrCodeService;
use App\Services\ReviewService;
use App\Services\TemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Pagination\LengthAwarePaginator;

class DashboardController extends Controller
{
    protected $qrCodeService;
    protected $reviewService;
    protected $templateService;

    /**
     * Create a new controller instance.
     *
     * @param QrCodeService $qrCodeService
     * @param ReviewService $reviewService
     * @param TemplateService $templateService
     */
    public function __construct(
        QrCodeService $qrCodeService,
        ReviewService $reviewService,
        TemplateService $templateService
    ) {
        $this->qrCodeService = $qrCodeService;
        $this->reviewService = $reviewService;
        $this->templateService = $templateService;
    }

    /**
     * Display the dashboard with summary statistics.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Check if user has a valid Supabase token
        if (!Session::has('supabase_token')) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to view the dashboard.');
        }
        
        // Get user ID from session or fetch it if not available
        $userId = Session::get('user_id');
        
        if (!$userId && Session::has('supabase_token')) {
            // Try to get user data from Supabase
            try {
                $supabaseKey = config('supabase.key');
                $supabaseResponse = \Illuminate\Support\Facades\Http::withHeaders([
                    'apikey' => $supabaseKey,
                    'Authorization' => 'Bearer ' . Session::get('supabase_token'),
                    'Content-Type' => 'application/json',
                ])->get(config('supabase.url') . '/auth/v1/user');
                
                if ($supabaseResponse->successful()) {
                    $userData = $supabaseResponse->json();
                    if (isset($userData['id'])) {
                        $userId = $userData['id'];
                        Session::put('user_id', $userId);
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Error fetching user data: ' . $e->getMessage());
            }
        }
        
        // If still no user ID, redirect to login
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'Unable to retrieve your account information. Please log in again.');
        }
        
        // Get summary statistics
        $stats = [
            'qr_codes_count' => $this->qrCodeService->countQrCodesForUser($userId),
            'reviews_count' => $this->reviewService->countReviewsForUser($userId),
            'templates_count' => $this->templateService->countTemplatesForUser($userId),
            'average_rating' => 0,
        ];
        
        // Get review stats including average rating
        $reviewStats = $this->reviewService->getReviewStats($userId);
        $stats['average_rating'] = $reviewStats['average_rating'];
        
        // Get recent reviews
        $recentReviews = $this->reviewService->getRecentReviewsForUser($userId, 5);
        
        // Convert to collection for blade template compatibility
        $recentReviews = collect($recentReviews)->map(function($review) {
            return (object) $review;
        });
        
        return view('pages.dashboard', compact('stats', 'recentReviews'));
    }
    
    /**
     * Display the QR codes management page.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function qrCodes(Request $request)
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to view QR codes.');
        }
        
        $page = $request->get('page', 1);
        $perPage = 10;
        
        // Get QR codes for the current page
        $qrCodes = $this->qrCodeService->getQrCodesForUser(
            $userId, 
            $perPage, 
            ($page - 1) * $perPage
        );
        
        // Get total count for pagination
        $total = $this->qrCodeService->countQrCodesForUser($userId);
        
        // Convert to collection for blade template compatibility
        $qrCodesCollection = collect($qrCodes)->map(function($qrCode) {
            return (object) $qrCode;
        });
        
        // Create a paginator instance
        $qrCodes = new LengthAwarePaginator(
            $qrCodesCollection,
            $total,
            $perPage,
            $page,
            ['path' => route('dashboard.qr-codes')]
        );
            
        return view('pages.dashboard.qr-codes', compact('qrCodes'));
    }
    
    /**
     * Display the form to create a new QR code.
     *
     * @return \Illuminate\View\View
     */
    public function createQrCode()
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to create QR codes.');
        }
        
        return view('pages.dashboard.create-qr-code');
    }
    
    /**
     * Store a newly created QR code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeQrCode(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);
        
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to create a QR code.');
        }
        
        // Create QR code using the service
        $qrCode = $this->qrCodeService->createQrCode(
            $userId,
            $request->name,
            $request->description
        );
        
        if (!$qrCode) {
            return redirect()->route('dashboard.qr-codes.create')
                ->with('error', 'Failed to create QR code. Please try again.')
                ->withInput();
        }
        
        return redirect()->route('dashboard.qr-codes')
            ->with('success', 'QR code created successfully!');
    }
    
    /**
     * Display the reviews management page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function reviews(Request $request)
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to view reviews.');
        }
        
        $page = $request->get('page', 1);
        $perPage = 10;
        
        // Build filters from request
        $filters = [];
        
        if ($request->has('rating') && $request->rating != '') {
            $filters['rating'] = $request->rating;
        }
        
        if ($request->has('start_date') && $request->has('end_date') &&
            $request->start_date && $request->end_date) {
            $filters['start_date'] = $request->start_date;
            $filters['end_date'] = $request->end_date;
        }
        
        // Get reviews for the current page with filters
        $reviews = $this->reviewService->getReviewsForUser(
            $userId,
            $filters,
            $perPage,
            ($page - 1) * $perPage
        );
        
        // Get total count for pagination
        $total = $this->reviewService->countReviewsForUser($userId, $filters);
        
        // Convert to collection for blade template compatibility
        $reviewsCollection = collect($reviews)->map(function($review) {
            return (object) $review;
        });
        
        // Create a paginator instance
        $reviews = new LengthAwarePaginator(
            $reviewsCollection,
            $total,
            $perPage,
            $page,
            ['path' => route('dashboard.reviews'), 'query' => $request->query()]
        );
        
        return view('pages.dashboard.reviews', compact('reviews'));
    }
    
    /**
     * Update a review with vendor response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function respondToReview(Request $request, $id)
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to respond to reviews.');
        }
        
        $request->validate([
            'vendor_response' => 'required|string|max:1000',
        ]);
        
        // Add response to the review using the service
        $result = $this->reviewService->addResponseToReview($id, $request->vendor_response);
        
        if (!$result) {
            return redirect()->route('dashboard.reviews')
                ->with('error', 'Failed to add response. Please try again.');
        }
        
        return redirect()->route('dashboard.reviews')
            ->with('success', 'Response added successfully!');
    }
    
    /**
     * Display the analytics page.
     *
     * @return \Illuminate\View\View
     */
    public function analytics()
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to view analytics.');
        }
        
        // Get monthly review counts for the past 6 months
        $monthlyReviews = $this->reviewService->getMonthlyReviewCounts($userId, 6);
        
        // Get review stats including rating distribution
        $reviewStats = $this->reviewService->getReviewStats($userId);
        $ratingDistribution = $reviewStats['rating_distribution'];
        
        return view('pages.dashboard.analytics', compact('monthlyReviews', 'ratingDistribution'));
    }
    
    /**
     * Display the templates management page.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function templates(Request $request)
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to view templates.');
        }
        
        $page = $request->get('page', 1);
        $perPage = 10;
        
        // Get templates for the current page
        $templates = $this->templateService->getTemplatesForUser(
            $userId,
            $perPage,
            ($page - 1) * $perPage
        );
        
        // Get total count for pagination
        $total = $this->templateService->countTemplatesForUser($userId);
        
        // Convert to collection for blade template compatibility
        $templatesCollection = collect($templates)->map(function($template) {
            return (object) $template;
        });
        
        // Create a paginator instance
        $templates = new LengthAwarePaginator(
            $templatesCollection,
            $total,
            $perPage,
            $page,
            ['path' => route('dashboard.templates')]
        );
            
        return view('pages.dashboard.templates', compact('templates'));
    }
    
    /**
     * Display the form to create a new template.
     *
     * @return \Illuminate\View\View
     */
    public function createTemplate()
    {
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to create templates.');
        }
        
        return view('pages.dashboard.create-template');
    }
    
    /**
     * Store a newly created template.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeTemplate(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'primary_color' => 'required|string|max:7',
            'secondary_color' => 'required|string|max:7',
            'content' => 'required|string',
        ]);
        
        $userId = Session::get('user_id');
        
        // Check if user is authenticated
        if (!$userId) {
            return redirect()->route('login')
                ->with('error', 'You must be logged in to create templates.');
        }
        
        // Handle logo upload
        $logo = null;
        if ($request->hasFile('logo')) {
            $logo = 'logo-' . uniqid() . '.' . $request->file('logo')->extension();
            // In a real implementation, we would upload the logo to storage
            // $request->file('logo')->storeAs('public/logos', $logo);
        }
        
        // Create template using the service
        $template = $this->templateService->createTemplate(
            $userId,
            $request->name,
            $request->description,
            $request->primary_color,
            $request->secondary_color,
            $request->content,
            $logo
        );
        
        if (!$template) {
            return redirect()->route('dashboard.templates.create')
                ->with('error', 'Failed to create template. Please try again.')
                ->withInput();
        }
        
        return redirect()->route('dashboard.templates')
            ->with('success', 'Template created successfully!');
    }
}
