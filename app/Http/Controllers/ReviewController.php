<?php

namespace App\Http\Controllers;

use App\Services\QrCodeService;
use App\Services\ReviewService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ReviewController extends Controller
{
    protected $qrCodeService;
    protected $reviewService;

    /**
     * Create a new controller instance.
     *
     * @param QrCodeService $qrCodeService
     * @param ReviewService $reviewService
     */
    public function __construct(
        QrCodeService $qrCodeService,
        ReviewService $reviewService
    ) {
        $this->qrCodeService = $qrCodeService;
        $this->reviewService = $reviewService;
    }

    /**
     * Display the review page for a QR code.
     *
     * @param string $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Get QR code by UUID
        $qrCode = $this->qrCodeService->getQrCodeByUuid($id);
        
        if (!$qrCode) {
            return view('pages.review.error', [
                'message' => 'QR code not found or has expired.'
            ]);
        }
        
        // Increment scan count
        $this->qrCodeService->incrementScanCount($qrCode['id']);
        
        // Get business name from QR code
        $businessName = $qrCode['name'];
        
        return view('pages.review.form', [
            'qrCode' => $qrCode,
            'businessName' => $businessName
        ]);
    }

    /**
     * Store a new review.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\View\View
     */
    public function store(Request $request, $id)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'additional_feedback' => 'nullable|string|max:1000',
        ]);
        
        // Get QR code by UUID
        $qrCode = $this->qrCodeService->getQrCodeByUuid($id);
        
        if (!$qrCode) {
            return view('pages.review.error', [
                'message' => 'QR code not found or has expired.'
            ]);
        }
        
        // Create review
        $reviewData = [
            'qr_code_id' => $qrCode['id'],
            'customer_name' => $request->customer_name,
            'customer_email' => $request->customer_email,
            'rating' => $request->rating,
            'comment' => $request->comment,
            'additional_feedback' => $request->additional_feedback,
            'is_published_to_google' => false
        ];
        
        $review = $this->reviewService->createReview($reviewData);
        
        if (!$review) {
            return view('pages.review.error', [
                'message' => 'Failed to submit review. Please try again.'
            ]);
        }
        
        // If rating is 5 stars, show Google Reviews option
        if ($request->rating == 5) {
            return view('pages.review.thank-you', [
                'showGoogleOption' => true,
                'businessName' => $qrCode['name'],
                'review' => $review
            ]);
        }
        
        return view('pages.review.thank-you', [
            'showGoogleOption' => false,
            'businessName' => $qrCode['name'],
            'review' => $review
        ]);
    }

    /**
     * Submit a review to Google.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitToGoogle(Request $request, $id)
    {
        // In a real implementation, this would connect to the Google Reviews API
        // For now, we'll just mark the review as published to Google
        
        $review = $this->reviewService->getReviewById($id);
        
        if (!$review) {
            return redirect()->route('review.thank-you', ['id' => $id])
                ->with('error', 'Review not found.');
        }
        
        // Update review to mark as published to Google
        $this->reviewService->updateReview($id, [
            'is_published_to_google' => true
        ]);
        
        return redirect()->route('review.thank-you', ['id' => $id])
            ->with('success', 'Your review has been submitted to Google!');
    }

    /**
     * Display the thank you page after submitting a review.
     *
     * @param string $id
     * @return \Illuminate\View\View
     */
    public function thankYou($id)
    {
        $review = $this->reviewService->getReviewById($id);
        
        if (!$review) {
            return view('pages.review.error', [
                'message' => 'Review not found.'
            ]);
        }
        
        $qrCode = $this->qrCodeService->getById($review['qr_code_id']);
        
        return view('pages.review.thank-you', [
            'showGoogleOption' => $review['rating'] == 5 && !$review['is_published_to_google'],
            'businessName' => $qrCode['name'],
            'review' => $review
        ]);
    }
}
