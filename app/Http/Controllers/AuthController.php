<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Str;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Display the login page.
     *
     * @return \Illuminate\View\View
     */
    public function showLogin()
    {
        return view('pages.auth.login');
    }

    /**
     * Display the registration page.
     *
     * @return \Illuminate\View\View
     */
    public function showRegister()
    {
        return view('pages.auth.register');
    }

    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // Check if Supabase token is provided
        if ($request->has('supabase_token') && !empty($request->supabase_token)) {
            try {
                $validated = $request->validate([
                    'email' => 'required|email',
                    'supabase_token' => 'required|string',
                    'remember' => 'boolean',
                ]);
                
                // Verify the Supabase token
                $supabaseKey = config('supabase.key'); // Get the API key
                $supabaseResponse = Http::withHeaders([
                    'apikey' => $supabaseKey, // Add the API key header
                    'Authorization' => 'Bearer ' . $validated['supabase_token'],
                    'Content-Type' => 'application/json',
                ])->get(config('supabase.url') . '/auth/v1/user');
                
                if ($supabaseResponse->failed()) {
                    \Log::error('Supabase token verification failed: ' . $supabaseResponse->body());
                    return back()->withErrors([
                        'email' => 'Failed to verify Supabase token. Please try again.',
                    ])->withInput($request->except('password'));
                }
                
                $userData = $supabaseResponse->json();
                \Log::info('Supabase user data: ' . json_encode($userData));
                
                // Ensure the email matches
                if ($userData['email'] !== $validated['email']) {
                    return back()->withErrors([
                        'email' => 'Email mismatch between Supabase and form data.',
                    ])->withInput($request->except('password'));
                }
                
                // Store Supabase token in session for authentication
                session(['supabase_token' => $validated['supabase_token']]);
                session(['user_email' => $userData['email']]);
                session(['user_name' => $userData['user_metadata']['name'] ?? explode('@', $userData['email'])[0]]);
                
                // Store user_id in session for dashboard access
                if (isset($userData['id'])) {
                    session(['user_id' => $userData['id']]);
                }
                
                // Make the session persistent
                config(['session.lifetime' => 43200]); // 30 days in minutes
                $request->session()->regenerate();
                
                // Set a long-lived cookie to help with session persistence
                cookie()->queue('rs_session_active', 'true', 43200);
                
                // Force redirect to dashboard if the flag is set
                if ($request->has('redirect_to_dashboard')) {
                    return redirect('/dashboard');
                }
                
                return redirect()->intended('/dashboard');
            } catch (ValidationException $e) {
                return back()->withErrors($e->errors())->withInput($request->except('password'));
            } catch (\Exception $e) {
                \Log::error('Login error: ' . $e->getMessage());
                return back()->withErrors([
                    'email' => 'An error occurred during login. Please try again.',
                ])->withInput($request->except('password'));
            }
        } else {
            // Traditional form submission (fallback)
            $credentials = $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);
            
            try {
                // Try to sign in with Supabase
                $supabaseUrl = config('supabase.url');
                $supabaseKey = config('supabase.key'); // Use anon key for signin
                
                $supabaseResponse = Http::withHeaders([
                    'apikey' => $supabaseKey,
                    'Content-Type' => 'application/json',
                ])->post($supabaseUrl . '/auth/v1/token?grant_type=password', [
                    'email' => $credentials['email'],
                    'password' => $credentials['password'],
                ]);
                
                if ($supabaseResponse->failed()) {
                    \Log::error('Supabase login failed: ' . $supabaseResponse->body());
                    return back()->withErrors([
                        'email' => 'The provided credentials do not match our records.',
                    ])->withInput($request->except('password'));
                }
                
                // Supabase login successful
                $signInData = $supabaseResponse->json();
                
                // Get user data from Supabase
                $userResponse = Http::withHeaders([
                    'apikey' => $supabaseKey, // Add the API key header
                    'Authorization' => 'Bearer ' . $signInData['access_token'],
                    'Content-Type' => 'application/json',
                ])->get(config('supabase.url') . '/auth/v1/user');
                
                if ($userResponse->failed()) {
                    \Log::error('Failed to get user data after login: ' . $userResponse->body());
                    
                    // Log additional information for debugging
                    \Log::info('Login attempt details', [
                        'supabase_url' => config('supabase.url'),
                        'has_token' => !empty($signInData['access_token']),
                        'token_length' => strlen($signInData['access_token'] ?? ''),
                        'has_api_key' => !empty($supabaseKey),
                    ]);
                    
                    return back()->withErrors([
                        'email' => 'Login successful but failed to get user data. Please try again.',
                    ])->withInput($request->except('password'));
                }
                
                $userData = $userResponse->json();
                
                // Store Supabase token in session for authentication
                session(['supabase_token' => $signInData['access_token']]);
                session(['user_email' => $userData['email']]);
                session(['user_name' => $userData['user_metadata']['name'] ?? explode('@', $userData['email'])[0]]);
                
                // Make the session persistent
                config(['session.lifetime' => 43200]); // 30 days in minutes
                $request->session()->regenerate();
                
                // Set a long-lived cookie to help with session persistence
                cookie()->queue('rs_session_active', 'true', 43200);
                
                return redirect()->intended('/dashboard');
            } catch (\Exception $e) {
                \Log::error('Login error: ' . $e->getMessage());
                return back()->withErrors([
                    'email' => 'An error occurred during login. Please try again.',
                ])->withInput($request->except('password'));
            }
        }
    }

    /**
     * Handle a registration request for the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        // Log the request data for debugging (excluding sensitive fields)
        \Log::info('Registration request received', [
            'has_token' => $request->has('supabase_token') && !empty($request->supabase_token),
            'email' => $request->email,
            'name' => $request->name,
        ]);
        
        // Check if Supabase token is provided
        if ($request->has('supabase_token') && !empty($request->supabase_token)) {
            try {
                // Validate the request
                $validated = $request->validate([
                    'name' => 'required|string|max:255',
                    'email' => 'required|string|email|max:255',
                    'supabase_token' => 'required|string',
                    'terms' => 'required|accepted',
                ]);
                
                // Verify the Supabase token
                $supabaseResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $validated['supabase_token'],
                ])->get(config('supabase.url') . '/auth/v1/user');
                
                if ($supabaseResponse->failed()) {
                    \Log::error('Supabase token verification failed: ' . $supabaseResponse->body());
                    return back()->withErrors([
                        'email' => 'Failed to verify Supabase token. Please try again.',
                    ])->withInput($request->except('password', 'password_confirmation'));
                }
                
                $userData = $supabaseResponse->json();
                \Log::info('Supabase user data: ' . json_encode($userData));
                
                // Ensure the email matches
                if ($userData['email'] !== $validated['email']) {
                    return back()->withErrors([
                        'email' => 'Email mismatch between Supabase and form data.',
                    ])->withInput($request->except('password', 'password_confirmation'));
                }
                
                // Store Supabase token in session for authentication
                session(['supabase_token' => $validated['supabase_token']]);
                session(['user_email' => $userData['email']]);
                session(['user_name' => $userData['user_metadata']['name'] ?? explode('@', $userData['email'])[0]]);
                
                return redirect('/dashboard');
            } catch (ValidationException $e) {
                \Log::error('Validation error during registration: ' . json_encode($e->errors()));
                return back()->withErrors($e->errors())->withInput($request->except('password', 'password_confirmation'));
            } catch (\Exception $e) {
                \Log::error('Registration error with token: ' . $e->getMessage());
                return back()->withErrors([
                    'email' => 'An error occurred during registration. Please try again.',
                ])->withInput($request->except('password', 'password_confirmation'));
            }
        } else {
            // Traditional form submission (fallback)
            \Log::info('Using traditional registration flow (no token provided)');
            
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255',
                'password' => 'required|string|min:8|confirmed',
                'terms' => 'required|accepted',
            ]);

            if ($validator->fails()) {
                \Log::error('Validation failed: ' . json_encode($validator->errors()->toArray()));
                return back()->withErrors($validator)->withInput($request->except('password', 'password_confirmation'));
            }

            try {
                // Create user in Supabase using service role key
                $supabaseUrl = config('supabase.url');
                $supabaseServiceKey = config('supabase.service_role');
                $supabaseAnonKey = config('supabase.key');
                
                // Make sure we have the service role key
                if (empty($supabaseServiceKey)) {
                    \Log::error('Supabase service role key is missing');
                    return back()->withErrors([
                        'email' => 'Server configuration error. Please contact support.',
                    ])->withInput($request->except('password', 'password_confirmation'));
                }
                
                // Use service role key for admin operations
                $supabaseKey = $supabaseServiceKey;
                
                \Log::info('Checking if user already exists in Supabase', ['email' => $request->email]);
                
                // Instead of using the admin/users endpoint which returns all users,
                // we'll try to directly create the user and handle the error if it exists
                
                // Log the attempt to create a user
                \Log::info('Attempting to create user in Supabase', [
                    'email' => $request->email,
                    'name' => $request->name
                ]);
                
                // Skip the existence check as it's not working correctly
                $userExists = false;
                
                if ($userExists) {
                    // User already exists in Supabase, return error
                    \Log::info('User already exists in Supabase', ['email' => $request->email]);
                    return back()->withErrors([
                        'email' => 'A user with this email already exists. Please try logging in instead.',
                    ])->withInput($request->except('password', 'password_confirmation'));
                }
                
                // Create user in Supabase
                \Log::info('Creating user in Supabase', ['email' => $request->email, 'name' => $request->name]);
                
                $supabaseResponse = Http::withHeaders([
                    'apikey' => $supabaseKey,
                    'Authorization' => 'Bearer ' . $supabaseKey,
                    'Content-Type' => 'application/json',
                ])->post($supabaseUrl . '/auth/v1/admin/users', [
                    'email' => $request->email,
                    'password' => $request->password,
                    'email_confirm' => true, // Skip email verification
                    'user_metadata' => [
                        'name' => $request->name,
                    ],
                ]);
                
                if ($supabaseResponse->failed()) {
                    \Log::error('Supabase user creation failed: ' . $supabaseResponse->body());
                    
                    // Extract error message from response
                    $errorData = $supabaseResponse->json();
                    $errorCode = $errorData['error_code'] ?? '';
                    $errorMessage = 'Please try again.';
                    
                    if (isset($errorData['error_description'])) {
                        $errorMessage = $errorData['error_description'];
                    } elseif (isset($errorData['msg'])) {
                        $errorMessage = $errorData['msg'];
                    }
                    
                    // If the error is that the email already exists, try to sign in instead
                    if ($errorCode === 'email_exists' || strpos($errorMessage, 'already been registered') !== false) {
                        \Log::info('User already exists, attempting to sign in', ['email' => $request->email]);
                        // Skip to sign in process
                    } else {
                        // For other errors, return with error message
                        return back()->withErrors([
                            'email' => 'Failed to create user in Supabase. ' . $errorMessage,
                        ])->withInput($request->except('password', 'password_confirmation'));
                    }
                }
                
                // Whether we created a new user or the user already existed, we need to sign them in
                \Log::info('Signing in user with Supabase', ['email' => $request->email]);
                
                // Sign in the user with Supabase to get a token
                $signInResponse = Http::withHeaders([
                    'apikey' => $supabaseAnonKey, // Use anon key for signin
                    'Content-Type' => 'application/json',
                ])->post($supabaseUrl . '/auth/v1/token?grant_type=password', [
                    'email' => $request->email,
                    'password' => $request->password,
                ]);
                
                if ($signInResponse->failed()) {
                    \Log::error('Supabase sign in failed: ' . $signInResponse->body());
                    
                    // Check if this is a password mismatch for an existing user
                    $signInError = $signInResponse->json();
                    $signInErrorMsg = $signInError['error_description'] ?? ($signInError['msg'] ?? 'Unknown error');
                    
                    if (strpos(strtolower($signInErrorMsg), 'password') !== false) {
                        return back()->withErrors([
                            'email' => 'A user with this email already exists but the password is incorrect. Please try logging in with the correct password.',
                        ])->withInput($request->except('password', 'password_confirmation'));
                    }
                    
                    return back()->withErrors([
                        'email' => 'Failed to sign in. ' . $signInErrorMsg,
                    ])->withInput($request->except('password', 'password_confirmation'));
                }
                
                $signInData = $signInResponse->json();
                \Log::info('User signed in successfully');
                
                // Store Supabase token in session for authentication
                session(['supabase_token' => $signInData['access_token']]);
                session(['user_email' => $request->email]);
                session(['user_name' => $request->name]);
                
                return redirect('/dashboard');
            } catch (\Exception $e) {
                \Log::error('Registration error: ' . $e->getMessage() . '\n' . $e->getTraceAsString());
                return back()->withErrors([
                    'email' => 'An error occurred during registration. Please try again.',
                ])->withInput($request->except('password', 'password_confirmation'));
            }
        }
    }

    /**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // Get Supabase token from session
        $supabaseToken = session('supabase_token');
        $userEmail = session('user_email');
        
        // If token exists, try to sign out from Supabase
        if ($supabaseToken) {
            try {
                $supabaseUrl = config('supabase.url');
                $supabaseKey = config('supabase.key');
                
                // Call Supabase signout endpoint
                $response = Http::withHeaders([
                    'apikey' => $supabaseKey,
                    'Authorization' => 'Bearer ' . $supabaseToken,
                    'Content-Type' => 'application/json',
                ])->post($supabaseUrl . '/auth/v1/logout');
                
                if ($response->successful()) {
                    \Log::info('User signed out from Supabase: ' . $userEmail);
                } else {
                    \Log::warning('Supabase logout returned non-success status: ' . $response->status() . ' - ' . $response->body());
                }
            } catch (\Exception $e) {
                \Log::error('Error signing out from Supabase: ' . $e->getMessage());
            }
        }

        // Clear session data
        session()->forget(['supabase_token', 'user_email', 'user_name']);
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        // Remove the persistent cookie
        cookie()->queue(cookie()->forget('rs_session_active'));
        
        // Store a flash message for the user
        session()->flash('status', 'You have been successfully logged out.');

        // Handle JSON requests if needed
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Logged out successfully',
                'success' => true
            ]);
        }

        // Redirect to home page
        return redirect('/');
    }
    
    /**
     * Get the current authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        if (Auth::check()) {
            return response()->json([
                'user' => Auth::user(),
                'authenticated' => true
            ]);
        }
        
        return response()->json([
            'user' => null,
            'authenticated' => false
        ]);
    }
}
