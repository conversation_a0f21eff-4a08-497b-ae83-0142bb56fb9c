<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SupabaseAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if Supabase token exists in session
        if (!session()->has('supabase_token')) {
            // If no token, redirect to login
            return redirect()->route('login')->with('error', 'Please log in to access this page.');
        }

        $token = session('supabase_token');

        try {
            // Get Supabase API key from config
            $supabaseKey = config('supabase.key');
            
            // Verify the token with Supabase
            $response = Http::withHeaders([
                'apikey' => $supabaseKey, // Add the API key header
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ])->get(config('supabase.url') . '/auth/v1/user');

            if ($response->failed()) {
                // Token is invalid or expired
                Log::warning('Invalid Supabase token: ' . $response->body());
                
                // Clear session and redirect to login
                session()->forget(['supabase_token', 'user_email', 'user_name']);
                return redirect()->route('login')->with('error', 'Your session has expired. Please log in again.');
            }

            // Token is valid, ensure user_id is set in session
            $userData = $response->json();
            if (isset($userData['id']) && !session()->has('user_id')) {
                session(['user_id' => $userData['id']]);
            }
            
            // Continue to the requested page
            return $next($request);
        } catch (\Exception $e) {
            Log::error('Error verifying Supabase token: ' . $e->getMessage());
            
            // Clear session and redirect to login
            session()->forget(['supabase_token', 'user_email', 'user_name']);
            return redirect()->route('login')->with('error', 'An error occurred. Please log in again.');
        }
    }
}
