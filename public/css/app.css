/* Import Tailwind CSS */
@import url('https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');

/* Import Fonts */
@import url('https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap');

/* Custom Color Variables */
:root {
  --primary-50: #e6f0ff;
  --primary-100: #cce0ff;
  --primary-200: #99c2ff;
  --primary-300: #66a3ff;
  --primary-400: #3385ff;
  --primary-500: #004cbf; /* Logo blue color */
  --primary-600: #003d99;
  --primary-700: #002e73;
  --primary-800: #001f4d;
  --primary-900: #000f26;
  --primary-950: #000a1a;
  
  --secondary-50: #e6fff6;
  --secondary-100: #ccffed;
  --secondary-200: #99ffdb;
  --secondary-300: #66ffc9;
  --secondary-400: #33ffb7;
  --secondary-500: #0dff96; /* Logo green color */
  --secondary-600: #00cc78;
  --secondary-700: #00995a;
  --secondary-800: #00663c;
  --secondary-900: #00331e;
  --secondary-950: #00190f;
}

/* Base Styles */
body {
  font-family: 'Figtree', sans-serif;
}

/* Direct color classes for elements using inline Tailwind-like classes */
.text-\[\#004cbf\] {
  color: #004cbf !important;
}

.text-\[\#0dff96\] {
  color: #0dff96 !important;
}

.bg-\[\#004cbf\] {
  background-color: #004cbf !important;
}

.bg-\[\#0dff96\] {
  background-color: #0dff96 !important;
}

.bg-\[\#003d99\] {
  background-color: #003d99 !important;
}

.bg-\[\#002966\] {
  background-color: #002966 !important;
}

/* Gradient backgrounds */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--gradient-from, transparent), var(--gradient-to, transparent));
}

.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--gradient-from, transparent), var(--gradient-to, transparent));
}

.from-\[\#004cbf\] {
  --gradient-from: #004cbf;
  background-image: linear-gradient(to right, #004cbf, var(--gradient-to, #0dff96));
}

.to-\[\#0dff96\] {
  --gradient-to: #0dff96;
  background-image: linear-gradient(to right, var(--gradient-from, #004cbf), #0dff96);
}

/* Fix for text with gradient */
.text-transparent.bg-clip-text.bg-gradient-to-r.from-\[\#004cbf\].to-\[\#0dff96\] {
  background-image: linear-gradient(to right, #004cbf, #0dff96);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Fix for opacity */
.opacity-40 {
  opacity: 0.4;
}

/* Additional utility classes */
.hover\:opacity-90:hover {
  opacity: 0.9;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}
