/**
 * Supabase Authentication Client
 * This file handles the Supabase authentication integration with the Laravel application.
 */

// Initialize Supabase client immediately to ensure it's available
(function initSupabase() {
    // Get Supabase credentials from meta tags
    const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.getAttribute('content');
    const supabaseKey = document.querySelector('meta[name="supabase-key"]')?.getAttribute('content');
    
    if (!supabaseUrl || !supabaseKey) {
        console.error('Supabase URL or Key is missing');
        return;
    }
    
    try {
        // Check if supabase global object is available
        if (typeof supabase === 'undefined') {
            console.error('Supabase global object is not available');
            return;
        }
        
        // Initialize Supabase client
        const { createClient } = supabase;
        if (!createClient) {
            console.error('Supabase createClient function is not available');
            return;
        }
        
        // Only create a new client if one doesn't already exist
        if (!window.supabaseClient) {
            console.log('Creating new Supabase client');
            window.supabaseClient = createClient(supabaseUrl, supabaseKey);
        } else {
            console.log('Supabase client already exists');
        }
        
        // Add the auth functions to the window object for Alpine.js to access
        window.signUpWithEmail = signUpWithEmail;
        window.signInWithEmail = signInWithEmail;
        
        // Wait for DOM to be fully loaded before initializing forms
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                initForms(window.supabaseClient);
            });
        } else {
            // DOM already loaded, initialize forms now
            initForms(window.supabaseClient);
        }
        
        console.log('Supabase client initialized successfully');
    } catch (error) {
        console.error('Error initializing Supabase client:', error);
    }
})();

/**
 * Initialize all forms
 */
function initForms(supabaseClient) {
    // Handle sign up form
    initSignupForm(supabaseClient);
    
    // Handle login form
    initLoginForm(supabaseClient);
    
    // Handle logout
    initLogoutForm(supabaseClient);
}

/**
 * Initialize the signup form
 */
function initSignupForm(supabaseClient) {
    const signUpForm = document.getElementById('registerForm');
    if (!signUpForm) return;
    
    // We don't need to add an event listener here since the form is using Alpine.js
    // The Alpine.js component in the form will call window.signUpWithEmail directly
    console.log('Registration form found with ID: registerForm');
}

/**
 * Initialize the login form
 */
function initLoginForm(supabaseClient) {
    // We're using Alpine.js for the login form with ID 'loginForm'
    // so we don't need to add event listeners here
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) {
        console.warn('Login form not found with ID: loginForm');
        return;
    }
    
    // Check if the form is already initialized with Alpine.js
    if (loginForm._x_dataStack) {
        console.log('Login form already initialized with Alpine.js');
        return;
    }
    
    console.log('Login form found with ID: loginForm, using Alpine.js for handling');
    
    // Verify that the email input field exists and is properly set up
    const emailInput = loginForm.querySelector('#email');
    if (emailInput) {
        console.log('Email input found:', emailInput);
    } else {
        console.warn('Email input not found in login form');
    }
}

/**
 * Initialize the logout form
 */
function initLogoutForm(supabaseClient) {
    const logoutForm = document.querySelector('form[action*="logout"]');
    if (!logoutForm) return;
    
    logoutForm.addEventListener('submit', async function(e) {
        // Prevent the default form submission temporarily
        e.preventDefault();
        
        try {
            // Sign out from Supabase
            await supabaseClient.auth.signOut();
            console.log('Signed out from Supabase');
        } catch (error) {
            console.error('Error signing out from Supabase:', error.message);
        }
        
        // Continue with the form submission to handle Laravel logout
        logoutForm.submit();
    });
}

/**
 * Show error message
 */
function showError(message) {
    const errorContainer = document.getElementById('auth-error');
    if (errorContainer) {
        errorContainer.textContent = message;
        errorContainer.classList.remove('hidden');
    } else {
        alert(message);
    }
}

/**
 * Sign out the current user
 */
window.signOut = async function() {
    try {
        if (!window.supabaseClient) {
            console.error('Supabase client not initialized');
            window.location.href = '/logout';
            return { data: null, error: new Error('Supabase client not initialized') };
        }
        
        // Sign out from Supabase
        const { error } = await window.supabaseClient.auth.signOut();
        if (error) throw error;
        
        // Redirect to Laravel logout route
        window.location.href = '/logout';
        return { data: true, error: null };
    } catch (error) {
        console.error('Error signing out:', error);
        // Even if Supabase logout fails, try to logout from Laravel
        window.location.href = '/logout';
        return { data: null, error };
    }
};

/**
 * Get the current user session
 */
window.getSession = async function() {
    try {
        if (!window.supabaseClient) {
            console.error('Supabase client not initialized');
            return { data: null, error: new Error('Supabase client not initialized') };
        }
        
        const { data, error } = await window.supabaseClient.auth.getSession();
        if (error) throw error;
        return { data, error: null };
    } catch (error) {
        console.error('Error getting session:', error.message);
        return { data: null, error };
    }
};

/**
 * Sign in with email and password
 * This function is used by the Alpine.js component in the login form
 */
async function signInWithEmail(email, password) {
    try {
        if (!window.supabaseClient) {
            console.error('Supabase client not initialized');
            return { data: null, error: new Error('Supabase client not initialized') };
        }
        
        if (!email || typeof email !== 'string') {
            console.error('Invalid email format:', email);
            return { data: null, error: new Error('Invalid email format') };
        }
        
        console.log('Signing in with Supabase:', { email });
        
        // Sign in with Supabase
        const { data, error } = await window.supabaseClient.auth.signInWithPassword({
            email,
            password
        });
        
        if (error) {
            console.error('Supabase signin error:', error.message);
            return { data: null, error };
        }
        
        console.log('Signin successful, returning data:', data);
        return { data, error: null };
    } catch (error) {
        console.error('Error signing in:', error.message);
        return { data: null, error };
    }
}

/**
 * Sign up with email and password
 * This function is used by the Alpine.js component in the register form
 */
async function signUpWithEmail(email, password, userData = {}) {
    try {
        if (!window.supabaseClient) {
            console.error('Supabase client not initialized');
            return { data: null, error: new Error('Supabase client not initialized') };
        }
        
        console.log('Signing up with Supabase:', { email, userData });
        
        // Sign up with Supabase
        const { data, error } = await window.supabaseClient.auth.signUp({
            email,
            password,
            options: {
                data: userData
            }
        });
        
        if (error) {
            console.error('Supabase signup error:', error.message);
            return { data: null, error };
        }
        
        // If we don't have a session yet, try to get one
        if (!data.session) {
            console.log('No session after signup, attempting to sign in');
            
            // Try to sign in to get a session
            const signInResult = await window.supabaseClient.auth.signInWithPassword({
                email,
                password
            });
            
            if (signInResult.error) {
                console.error('Error signing in after registration:', signInResult.error.message);
                return { 
                    data: { 
                        user: data.user,
                        session: null,
                        email: email,  // Add email explicitly
                        user_metadata: userData // Add user metadata explicitly
                    }, 
                    error: null 
                };
            }
            
            return { 
                data: signInResult.data,
                error: null 
            };
        }
        
        // Ensure we return the email and user metadata even if Supabase doesn't
        if (data && data.user) {
            // Make sure user metadata is present
            if (!data.user.user_metadata) {
                data.user.user_metadata = userData;
            }
            // Make sure email is accessible
            if (!data.email) {
                data.email = email;
            }
        }
        
        console.log('Signup successful, returning data:', data);
        return { data, error: null };
    } catch (error) {
        console.error('Error signing up:', error.message);
        return { data: null, error };
    }
}
