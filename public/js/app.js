// Import Alpine.js from CDN
document.addEventListener('DOMContentLoaded', function() {
    // Load Alpine.js from CDN
    const alpineScript = document.createElement('script');
    alpineScript.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js';
    alpineScript.defer = true;
    document.head.appendChild(alpineScript);
    
    // Initialize any custom JavaScript here
    console.log('RatingShield app initialized');
});

// Custom JavaScript for the application
window.addEventListener('load', function() {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});
