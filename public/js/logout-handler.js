/**
 * Logo<PERSON> Handler
 * This script handles the logout process for both Supabase and Laravel.
 */

// Function to handle the logout process
function handleLogout(event) {
    event.preventDefault();
    console.log('Logout initiated');
    
    // Try to sign out from Supabase first
    if (window.supabaseClient) {
        console.log('Signing out from Supabase');
        window.supabaseClient.auth.signOut()
            .then(() => {
                console.log('Supabase signout successful');
                // Then submit the form to log out from Laravel
                document.getElementById('logout-form').submit();
            })
            .catch(error => {
                console.error('Supabase signout error:', error);
                // Even if Supabase logout fails, still log out from Laravel
                document.getElementById('logout-form').submit();
            });
    } else {
        console.log('Supabase client not available, proceeding with Laravel logout');
        // If Supabase client is not available, just log out from Laravel
        document.getElementById('logout-form').submit();
    }
}

// Add event listener when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    const logoutForm = document.getElementById('logout-form');
    if (logoutForm) {
        logoutForm.addEventListener('submit', handleLogout);
    }
});
