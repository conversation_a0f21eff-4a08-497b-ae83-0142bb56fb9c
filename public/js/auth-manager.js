/**
 * Authentication Manager
 * Handles authentication state management, logout, and session persistence
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Alpine.js data for authentication state
    window.handleLogout = async function() {
        try {
            console.log('Logging out...');
            
            // First sign out from Supabase if client is available
            if (window.supabaseClient) {
                await window.supabaseClient.auth.signOut();
                console.log('Signed out from Supabase');
            }
            
            // Then submit the form to handle server-side logout
            document.getElementById('logout-form').submit();
        } catch (error) {
            console.error('Error during logout:', error);
            // If there's an error, still try to submit the form
            document.getElementById('logout-form').submit();
        }
    };
    
    // Check authentication state on page load
    const checkAuthState = async function() {
        try {
            if (window.supabaseClient) {
                const { data, error } = await window.supabaseClient.auth.getSession();
                
                if (error) {
                    console.error('Error checking auth state:', error.message);
                    return;
                }
                
                // If we have a session but the page doesn't know about it yet, reload
                const isAuthenticated = document.querySelector('nav').__x.$data.isAuthenticated;
                const hasSession = data && data.session && data.session.access_token;
                
                if (hasSession && !isAuthenticated) {
                    console.log('Session found but page shows logged out, reloading...');
                    window.location.reload();
                } else if (!hasSession && isAuthenticated) {
                    console.log('No session found but page shows logged in, reloading...');
                    window.location.reload();
                }
            }
        } catch (error) {
            console.error('Error in checkAuthState:', error);
        }
    };
    
    // Run the check once on page load
    setTimeout(checkAuthState, 500);
});
