<html lang="en"><head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Nexus – Empowering Innovation Through Stories</title>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<style>
  body { font-family: 'Inter', sans-serif; }
  .fade-in { opacity: 0; transform: translateY(20px); animation: fadeInUp 0.8s ease-out forwards; }
  .fade-in-delay-1 { animation-delay: 0.1s; }
  .fade-in-delay-2 { animation-delay: 0.2s; }
  .fade-in-delay-3 { animation-delay: 0.3s; }
  .fade-in-delay-4 { animation-delay: 0.4s; }
  .fade-in-delay-5 { animation-delay: 0.5s; }
  .fade-in-delay-6 { animation-delay: 0.6s; }
  .fade-in-delay-7 { animation-delay: 0.7s; }
  .fade-in-delay-8 { animation-delay: 0.8s; }
  @keyframes fadeInUp {
    to { opacity: 1; transform: translateY(0); }
  }
  .glass-card { 
    backdrop-filter: blur(8px); 
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .gradient-border {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(168, 85, 247, 0.2));
    padding: 1px;
    border-radius: 16px;
  }
  .gradient-border-inner {
    background: rgb(15, 23, 42);
    border-radius: 15px;
    height: 100%;
    width: 100%;
  }
  .glow-card {
    box-shadow: 0 0 40px rgba(34, 197, 94, 0.15);
  }
  .pricing-card-popular {
    position: relative;
    transform: scale(1.05);
    z-index: 10;
  }
  @media (max-width: 768px) {
    .pricing-card-popular {
      transform: scale(1);
    }
  }
</style>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=IBM+Plex+Serif:wght@300;400;500;600;700&amp;family=IBM+Plex+Mono:wght@300;400;500;600;700&amp;family=Inter&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=IBM+Plex+Serif:wght@300;400;500;600;700&amp;family=IBM+Plex+Mono:wght@300;400;500;600;700&amp;family=Inter&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=IBM+Plex+Serif:wght@300;400;500;600;700&amp;family=IBM+Plex+Mono:wght@300;400;500;600;700&amp;family=Inter&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=IBM+Plex+Serif:wght@300;400;500;600;700&family=IBM+Plex+Mono:wght@300;400;500;600;700&family=Inter&display=swap" rel="stylesheet">
</head>
<body class="min-h-screen antialiased overflow-x-hidden text-slate-100 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
  <!-- Background Effects -->
  <div class="fixed inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-green-400/10 via-transparent to-transparent pointer-events-none"></div>
  <div class="fixed inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-purple-400/5 via-transparent to-transparent pointer-events-none"></div>
  
  <!-- Navigation -->
  <nav class="relative z-10 max-w-7xl mx-auto px-6 py-6 fade-in">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="hexagon" class="lucide lucide-hexagon w-4 h-4 text-white"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path></svg>
        </div>
        <span class="text-xl font-semibold tracking-tight">Nexus</span>
      </div>
      <div class="hidden md:flex items-center space-x-8 text-sm">
        <a href="#" class="text-slate-400 hover:text-white transition-colors">Articles</a>
        <a href="#" class="text-slate-400 hover:text-white transition-colors">Authors</a>
        <a href="#" class="text-slate-400 hover:text-white transition-colors">Categories</a>
        <button class="bg-white text-slate-900 px-4 py-2 rounded-lg font-medium hover:bg-slate-100 transition-all duration-200 hover:scale-105">
          Get Started
        </button>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mr-auto ml-auto pt-16 pr-6 pb-8 pl-6">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 auto-rows-[180px] gap-4">
      
      <!-- Hero Section -->
      <div class="col-span-1 sm:col-span-2 lg:col-span-3 row-span-2 flex flex-col fade-in pt-8 pr-8 pb-8 pl-8 justify-center">
        <div class="mb-6">
          <div class="inline-flex items-center space-x-2 bg-green-500/10 border border-green-500/20 px-3 py-1 rounded-full text-xs font-medium text-green-400 mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-3 h-3"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg>
            <span class="">Meet Nexus Platform</span>
          </div>
          <h1 class="sm:text-5xl lg:text-6xl leading-tight text-4xl font-bold tracking-tight mb-4">
            Empowering <span class="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">innovation</span>
            through stories
          </h1>
          <p class="leading-relaxed max-w-lg text-lg text-slate-400">
            Where technology meets creativity. Discover insights, share knowledge, and build the future together.
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <button class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 hover:scale-105 shadow-lg">
            Start Reading
          </button>
          <button class="border border-slate-600 text-slate-300 px-6 py-3 rounded-lg font-medium hover:border-slate-500 hover:text-white transition-all duration-200">
            Learn More
          </button>
        </div>
      </div>

      <!-- Featured Authors -->
      <div class="glass-card rounded-2xl p-6 flex flex-col justify-between col-span-1 lg:col-span-2 fade-in fade-in-delay-1 hover:scale-105 transition-transform duration-300">
        <div class="">
          <div class="flex items-center space-x-2 mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="users" class="lucide lucide-users w-5 h-5 text-green-400"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>
            <h3 class="text-lg font-semibold">Featured Authors</h3>
          </div>
          <p class="text-sm text-slate-400 mb-4">Our community of expert writers and thought leaders</p>
        </div>
        <div class="flex -space-x-3">
          <img src="https://images.unsplash.com/photo-1460904577954-8fadb262612c?w=1080&amp;q=80" class="w-12 h-12 object-cover border-slate-700 border-2 rounded-full" alt="Sarah Chen">
          <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&amp;h=60&amp;fit=crop&amp;crop=face" class="w-12 h-12 rounded-full border-2 border-slate-700" alt="Marcus Rodriguez">
          <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&amp;h=60&amp;fit=crop&amp;crop=face" class="w-12 h-12 rounded-full border-2 border-slate-700" alt="Elena Kowalski">
          <div class="w-12 h-12 rounded-full border-2 border-slate-700 bg-slate-800 flex items-center justify-center text-xs font-medium">
            +12
          </div>
        </div>
      </div>

      <!-- Latest Articles -->
      <div class="gradient-border fade-in fade-in-delay-2 hover:scale-105 transition-transform duration-300">
        <div class="gradient-border-inner flex flex-col h-full glow-card pt-6 pr-6 pb-6 pl-6 justify-between">
  <div class="flex space-x-3 items-start">
    
    <div class="">
      <h3 class="text-lg font-semibold mb-1">Latest Articles</h3>
      <p class="text-sm text-slate-400">Fresh insights on tech, design, and innovation</p>
    </div>
  </div>
  <div class="text-right">
    <span class="text-2xl font-bold text-green-400">247</span>
    <p class="text-xs text-slate-500">published this month</p>
  </div>
</div>
      </div>

      <!-- Newsletter -->
      <div class="glass-card flex flex-col col-span-1 sm:col-span-1 lg:col-span-1 row-span-2 fade-in fade-in-delay-3 hover:scale-105 transition-transform duration-300 rounded-2xl pt-6 pr-6 pb-6 pl-6 justify-between">
        <div class="flex justify-center mb-4">
          <div class="w-16 h-16 flex bg-gradient-to-r from-green-400 to-emerald-400 rounded-2xl items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="mail" class="lucide lucide-mail w-8 h-8 text-white"><path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path><rect x="2" y="4" width="20" height="16" rx="2"></rect></svg>
          </div>
        </div>
        <div class="text-center">
          <h3 class="text-lg font-semibold mb-2">Weekly Digest</h3>
          <p class="text-sm text-slate-400 mb-4">Get curated content delivered to your inbox</p>
          <button class="w-full bg-slate-800 border border-slate-700 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-slate-700 transition-colors">
            Subscribe
          </button>
        </div>
      </div>

      <!-- Categories -->
      <div class="glass-card flex flex-col fade-in fade-in-delay-4 hover:scale-105 transition-transform duration-300 rounded-2xl pt-6 pr-6 pb-6 pl-6 justify-between">
  <div class="flex items-center space-x-2 mb-3">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="grid-3x3" class="lucide lucide-grid-3x3 w-5 h-5 text-orange-400"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path><path d="M9 3v18"></path><path d="M15 3v18"></path></svg>
    <h3 class="text-lg font-semibold tracking-tight">Categories</h3>
  </div>
  <div class="">
    <span class="text-3xl font-semibold text-orange-400">20+</span>
    <p class="text-sm text-slate-400">New topics to discover</p>
  </div>
</div><div class="glass-card flex flex-col fade-in fade-in-delay-4 hover:scale-105 transition-transform duration-300 rounded-2xl pt-6 pr-6 pb-6 pl-6 justify-between">
  <div class="">
    <span class="text-3xl font-semibold text-blue-500">300</span>
    <p class="text-sm text-slate-400">Topics to explore now</p>
  </div>
  <div class="flex mb-3 space-x-2 items-center">
    <h3 class="text-lg font-semibold">Videos</h3>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="video" class="lucide lucide-video lucide-grid-3x3 w-[20px] h-[20px]" style="color: rgb(59, 130, 246);"><path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path><rect x="2" y="6" width="14" height="12" rx="2"></rect></svg>
  </div>
</div>

      <!-- Testimonial -->
      <div class="glass-card flex flex-col col-span-1 sm:col-span-2 lg:col-span-3 fade-in fade-in-delay-5 hover:scale-105 transition-transform duration-300 rounded-2xl pt-6 pr-6 pb-6 pl-6 justify-between">  
  <div class="flex mb-4 space-x-4 items-center">
    <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=60&amp;h=60&amp;fit=crop&amp;crop=face" class="w-12 h-12 rounded-full" alt="Isabella Chen">
    <div class="">
      <p class="font-semibold">Isabella Chen</p>
      <p class="text-xs text-slate-400">CEO &amp; Founder, TechVision</p>
    </div>
    <div class="flex text-yellow-400 ml-auto">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="star" class="lucide lucide-star w-4 h-4 fill-current"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="star" class="lucide lucide-star w-4 h-4 fill-current"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="star" class="lucide lucide-star w-4 h-4 fill-current"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg>
    </div>
  </div>
  <div class="text-base font-normal text-white mt-4">"Nexus revolutionizes collaboration with its seamless platform, fostering creativity and connection like never before."</div>
</div>

      <!-- Global Community -->
      <div class="glass-card rounded-2xl p-6 flex flex-col justify-center items-center text-center fade-in fade-in-delay-6 hover:scale-105 transition-transform duration-300">
        <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="globe" class="lucide lucide-globe w-6 h-6 text-white"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
        </div>
        <h3 class="text-sm font-semibold mb-1">Global Reach</h3>
        <p class="text-xs text-slate-400">50+ countries</p>
      </div>

      <!-- Creator Hub -->
      <div class="glass-card flex flex-col fade-in fade-in-delay-7 hover:scale-105 transition-transform duration-300 text-center rounded-2xl pt-6 pr-6 pb-6 pl-6 items-center justify-center">
        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="pen-tool" class="lucide lucide-pen-tool w-6 h-6 text-white"><path d="M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z"></path><path d="m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18"></path><path d="m2.3 2.3 7.286 7.286"></path><circle cx="11" cy="11" r="2"></circle></svg>
        </div>
        <h3 class="text-sm font-semibold mb-1">Creator Hub</h3>
        <p class="text-xs text-slate-400">Share your story</p>
      </div>

    </div>
  </div>

  <!-- Pricing Section -->
  <div class="max-w-7xl mr-auto ml-auto pt-16 pr-6 pb-16 pl-6">
    <!-- Pricing Header -->
    <div class="text-center mb-16 fade-in">
      <div class="inline-flex items-center space-x-2 bg-purple-500/10 border border-purple-500/20 px-3 py-1 rounded-full text-xs font-medium text-purple-400 mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="zap" class="lucide lucide-zap w-3 h-3"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg>
        <span>Pricing Plans</span>
      </div>
      <h2 class="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight mb-4">
        Simple, <span class="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">transparent</span> pricing
      </h2>
      <p class="text-lg text-slate-400 max-w-2xl mx-auto">
        Choose the perfect plan for your creative journey. All plans include our core features with no hidden fees.
      </p>
    </div>

    <!-- Pricing Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-16">
      
      <!-- Basic Plan -->
      <div class="glass-card rounded-2xl p-8 flex flex-col fade-in fade-in-delay-1 hover:scale-105 transition-all duration-300">
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-2">Basic</h3>
          <p class="text-slate-400 text-sm mb-6">Perfect for individual creators getting started</p>
          <div class="flex items-baseline mb-2">
            <span class="text-4xl font-bold text-green-400">$9</span>
            <span class="text-slate-400 ml-2">/month</span>
          </div>
          <p class="text-xs text-slate-500">Billed monthly</p>
        </div>
        
        <ul class="space-y-4 mb-8 flex-grow">
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Unlimited stories</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Basic analytics dashboard</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Community access</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Email support</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Mobile app access</span>
          </li>
        </ul>
        
        <button class="w-full bg-slate-800 border border-slate-700 text-white px-6 py-3 rounded-lg font-medium hover:bg-slate-700 hover:border-slate-600 transition-all duration-200">
          Get Started
        </button>
      </div>

      <!-- Pro Plan (Popular) -->
      <div class="glass-card rounded-2xl p-8 flex flex-col fade-in fade-in-delay-2 pricing-card-popular glow-card relative">
        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span class="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-1 rounded-full text-xs font-medium">
            Most Popular
          </span>
        </div>
        
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-2">Pro</h3>
          <p class="text-slate-400 text-sm mb-6">Ideal for growing creators and small teams</p>
          <div class="flex items-baseline mb-2">
            <span class="text-4xl font-bold text-green-400">$29</span>
            <span class="text-slate-400 ml-2">/month</span>
          </div>
          <p class="text-xs text-slate-500">Billed monthly</p>
        </div>
        
        <ul class="space-y-4 mb-8 flex-grow">
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Everything in Basic</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Advanced analytics &amp; insights</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Priority support</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Custom branding</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Team collaboration (up to 5)</span>
          </li>
        </ul>
        
        <button class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 hover:scale-105">
          Start Pro Trial
        </button>
      </div>

      <!-- Enterprise Plan -->
      <div class="glass-card rounded-2xl p-8 flex flex-col fade-in fade-in-delay-3 hover:scale-105 transition-all duration-300">
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-2">Enterprise</h3>
          <p class="text-slate-400 text-sm mb-6">For large organizations and enterprises</p>
          <div class="flex items-baseline mb-2">
            <span class="text-4xl font-bold text-green-400">$99</span>
            <span class="text-slate-400 ml-2">/month</span>
          </div>
          <p class="text-xs text-slate-500">Billed monthly</p>
        </div>
        
        <ul class="space-y-4 mb-8 flex-grow">
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Everything in Pro</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Unlimited team members</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>Dedicated account manager</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>SSO &amp; advanced security</span>
          </li>
          <li class="flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="check" class="lucide lucide-check w-4 h-4 text-green-400 mr-3 flex-shrink-0"><path d="M20 6 9 17l-5-5"></path></svg>
            <span>24/7 phone support</span>
          </li>
        </ul>
        
        <button class="w-full bg-slate-800 border border-slate-700 text-white px-6 py-3 rounded-lg font-medium hover:bg-slate-700 hover:border-slate-600 transition-all duration-200">
          Contact Sales
        </button>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="text-center mb-8 fade-in fade-in-delay-4">
      <p class="text-slate-400">
        Have questions? <a href="#" class="text-green-400 hover:text-green-300 transition-colors">Contact our support team</a> or check out our
        <a href="#" class="text-green-400 hover:text-green-300 transition-colors">FAQ section</a>.
      </p>
    </div>
  </div>

  <!-- Footer -->
  <footer class="max-w-7xl mx-auto px-6 py-16 border-t border-slate-800">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8 fade-in">
      <div class="">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="hexagon" class="lucide lucide-hexagon w-4 h-4 text-white"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path></svg>
          </div>
          <span class="text-xl font-semibold tracking-tight">Nexus</span>
        </div>
        <p class="text-slate-400 text-sm mb-4">
          Empowering innovation through stories. Join our community of creators and thought leaders.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="text-slate-400 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path></svg>
          </a>
          <a href="#" class="text-slate-400 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg>
          </a>
          <a href="#" class="text-slate-400 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path></svg>
          </a>
        </div>
      </div>
      
      <div class="">
        <h4 class="font-semibold mb-4">Product</h4>
        <ul class="space-y-2 text-sm text-slate-400">
          <li><a href="#" class="hover:text-white transition-colors">Features</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Pricing</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Integrations</a></li>
          <li><a href="#" class="hover:text-white transition-colors">API</a></li>
        </ul>
      </div>
      
      <div class="">
        <h4 class="font-semibold mb-4">Company</h4>
        <ul class="space-y-2 text-sm text-slate-400">
          <li><a href="#" class="hover:text-white transition-colors">About</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Press</a></li>
        </ul>
      </div>
      
      <div class="">
        <h4 class="font-semibold mb-4">Support</h4>
        <ul class="space-y-2 text-sm text-slate-400">
          <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Status</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Community</a></li>
        </ul>
      </div>
    </div>
    
    <div class="pt-8 border-t border-slate-800 flex flex-col md:flex-row justify-between items-center fade-in fade-in-delay-1">
      <p class="text-slate-400 text-sm">© 2024 Nexus. All rights reserved.</p>
      <div class="flex space-x-6 mt-4 md:mt-0">
        <a href="#" class="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
        <a href="#" class="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
        <a href="#" class="text-slate-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
      </div>
    </div>
  </footer>


</body></html>