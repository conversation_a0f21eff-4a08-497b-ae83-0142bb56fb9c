# Software Requirements Specification (SRS) for QR Review System (SLC Version)

## 1. Introduction

### 1.1 Purpose
The QR Review System aims to provide small to medium-sized local businesses with a direct, efficient, and user-friendly platform for collecting customer feedback through QR codes. This system addresses the limitations of existing review platforms by offering enhanced vendor control, authenticity, and actionable insights.

### 1.2 Scope
The QR Review System will allow vendors to generate unique QR codes for customer feedback, manage reviews through a secure dashboard, and gain insights into customer sentiment. The system will cater to various business types, including cafes, salons, clinics, and retail shops.

## 2. Overall Description

### 2.1 Product Perspective
The QR Review System will be a standalone web application that facilitate seamless feedback collection. It will consist of a customer-facing review page and a vendor dashboard.

### 2.2 Product Functions
- **QR Code Generation**: Vendors can generate unique QR codes for customer feedback.
- **Mobile-Friendly Review Page**: Customers can submit feedback via a mobile-optimized page.
- **Conditional Logic for Feedback**: The system will prompt customers for additional feedback if they rate less than 5 stars.
- **Vendor Dashboard**: Vendors can view, filter, and respond to reviews.
- **Analytics and Reporting**: The system will provide insights into review trends and customer sentiment.
- **Google Reviews Integration**: After a customer submits a five-star review, the system will connect to the Google Reviews API to submit this five-star review to Google Reviews.
- **Template Creation**: Vendors can create fully customizable, branded templates to collect customer reviews as an alternative to QR codes, with tracking capabilities for email views and review submissions with option to share via email and social media. 
- **Landing Page**: A public-facing landing page showcasing the product features, benefits, and subscription plans to attract potential customers.
- **Subscription Management**: Tiered subscription plans with different feature sets and pricing options.
- **Payment Processing**: Integration with payment gateways (Stripe and PayPal) to handle subscription billing and payments.

### 2.3 User Classes and Characteristics
- **Vendors**: Small to medium-sized business owners who will use the system to collect and manage customer feedback.
- **Customers**: End-users who will provide feedback through the QR codes.

### 2.4 Operating Environment
The system will be web-based, accessible via modern web browsers on desktop and mobile devices. It will be hosted on a cloud platform to ensure scalability and reliability.

### 2.5 Technology Stack
The QR Review System will be built using the following technology stack:
- **Frontend and Backend**: Laravel PHP framework for both frontend views and backend API development, providing a unified development experience
- **Database**: Supabase (PostgreSQL) for data storage and real-time capabilities
- **Authentication**: Laravel Sanctum and Supabase Auth for user authentication and authorization
- **QR Code Generation**: Native libraries integrated with Laravel
- **Cloud Infrastructure**: AWS/Google Cloud for hosting and scaling
- **Email Service**: Laravel Mailable with SMTP integration for email delivery
- **Analytics**: Custom analytics built with Laravel and Supabase for tracking user engagement
- **Payment Processing**: Stripe and PayPal API integrations for subscription billing and payment handling

## 3. Specific Requirements

### 3.1 Functional Requirements

#### 3.1.1 QR Code Generation
- The system shall allow vendors to generate unique QR codes for their business.
- The QR codes shall be downloadable and printable.

#### 3.1.2 Review Submission
- The system shall direct customers to a mobile-friendly review page upon scanning the QR code.
- The review page shall display the business name and allow customers to submit star ratings and comments.

#### 3.1.3 Conditional Logic
- If a customer rates less than 5 stars, the system shall prompt them to provide specific feedback.

#### 3.1.4 Vendor Dashboard
- The dashboard shall allow vendors to view all submitted reviews, including star ratings and comments.
- Vendors shall be able to filter reviews by rating and date.
- Vendors shall have the ability to respond to reviews.

#### 3.1.5 Analytics and Reporting
- The system shall provide monthly summary reports to vendors, including review statistics and key feedback themes.

#### 3.1.6 Google Reviews Integration
- The system shall connect to the Google Reviews API to submit five-star reviews after a customer submits them.

#### 3.1.7 Template Creation
- The system shall allow vendors to create customizable email templates to collect customer reviews as an alternative to QR codes.
- Templates shall support full branding customization including logos, colors, and styling to match the vendor's brand identity.
- The system shall track and report analytics on email engagement, including who viewed the email and who submitted a review.
- The system support option to share template via email and social media.

#### 3.1.8 Landing Page
- The system shall provide a public-facing landing page that showcases the product's features and benefits.
- The landing page shall include information about available subscription plans and pricing.
- The landing page shall have clear call-to-action elements for user registration and plan selection.

#### 3.1.9 Subscription Management
- The system shall offer tiered subscription plans (Free, Starter, Pro, Enterprise) with varying feature sets and usage limits.
- Users shall be able to select and upgrade/downgrade their subscription plans.
- The system shall enforce feature access based on the user's current subscription plan.
- The system shall provide a subscription management interface for users to view their current plan, billing history, and update payment information.

#### 3.1.10 Payment Processing
- The system shall integrate with Stripe and PayPal payment gateways to process subscription payments.
- The payment system shall support automatic recurring billing based on the subscription cycle.
- The system shall generate and email invoices to users for successful payments.
- The system shall handle payment failures and implement retry logic with appropriate notifications.
- The system shall provide secure storage of payment information in compliance with PCI-DSS standards.

### 3.2 Non-Functional Requirements

#### 3.2.1 Performance
- The system shall handle up to 10,000 concurrent users without performance degradation.

#### 3.2.2 Security
- The system shall implement secure authentication for vendor logins.
- Customer data shall be stored securely and comply with data protection regulations.

#### 3.2.3 Usability
- The user interface shall be intuitive and easy to navigate for both vendors and customers.

#### 3.2.4 Scalability
- The system shall be designed to scale horizontally to accommodate an increasing number of users and reviews.

## 4. Use Cases

### 4.1 Use Case 1: Vendor Generates QR Code
- **Actors**: Vendor
- **Preconditions**: Vendor is logged into the dashboard.
- **Postconditions**: A unique QR code is generated and available for download.
- **Main Flow**:
  1. Vendor selects the option to generate a QR code.
  2. The system generates a unique QR code.
  3. The vendor downloads the QR code.

### 4.2 Use Case 2: Customer Submits Review
- **Actors**: Customer
- **Preconditions**: Customer scans the QR code.
- **Postconditions**: Review is submitted and stored in the system.
- **Main Flow**:
  1. Customer scans the QR code.
  2. The system displays the review page.
  3. Customer submits a star rating and optional comments.
  4. If the rating is less than 5 stars, the system prompts for additional feedback.
  5. If the rating is 5 stars, the system connects to the Google Reviews API to submit the review and allows the customer to create an email template to send the review.

### 4.3 Use Case 3: Submit Five-Star Review to Google
- **Actors**: Customer
- **Preconditions**: Customer has submitted a five-star review.
- **Postconditions**: The five-star review is submitted to Google Reviews via the API.
- **Main Flow**:
  1. Customer submits a five-star review.
  2. The system connects to the Google Reviews API.
  3. The system submits the five-star review to Google.

### 4.4 Use Case 4: Create Template for Review Collection
- **Actors**: Vendor
- **Preconditions**: Vendor is logged into the dashboard.
- **Postconditions**: A branded email template is created and ready to be sent to customers.
- **Main Flow**:
  1. Vendor selects the option to create an email template.
  2. The system presents customization options for branding (logo, colors, styling).
  3. Vendor customizes the template and saves it.
  4. Vendor enters customer email addresses or imports from their database.
  5. Vendor sends review request emails.
  6. The system tracks email opens and review submissions for analytics.
  7. The system provide option to share via email and social media.

### 4.5 Use Case 5: Subscribe to a Paid Plan
- **Actors**: Vendor
- **Preconditions**: Vendor has created an account and is logged in.
- **Postconditions**: Vendor has successfully subscribed to a paid plan and gained access to associated features.
- **Main Flow**:
  1. Vendor navigates to the subscription management section.
  2. Vendor selects a subscription plan.
  3. System displays payment options (Stripe or PayPal).
  4. Vendor selects preferred payment method and enters payment details.
  5. System processes payment through the selected gateway.
  6. System confirms successful subscription and updates vendor's account permissions.
  7. System sends confirmation email with subscription details.

## 6. Implementation Plan

### 6.1 Project Timeline
The QR Review System will be developed and deployed within a 2-month timeframe, with the following high-level phases:

| Phase | Duration | Timeline |
|-------|----------|----------|
| Planning and Requirements Finalization | 1 week | Week 1 |
| Design and Architecture | 1 week | Week 2 |
| Core Development | 4 weeks | Weeks 3-6 |
| Testing and Quality Assurance | 1 week | Week 7 |
| Deployment and Launch | 1 week | Week 8 |

### 6.2 Development Milestones

#### Milestone 1: Project Setup and Basic Infrastructure (End of Week 2)
- Complete system architecture design
- Set up development environment
- Establish database schema
- Configure cloud infrastructure

#### Milestone 2: Core Functionality (End of Week 4)
- QR code generation feature
- Review submission flow
- Basic vendor dashboard
- User authentication

#### Milestone 3: Enhanced Features (End of Week 6)
- Conditional logic for reviews
- Google Reviews integration
- Template customization
- Analytics tracking

#### Milestone 4: System Completion (End of Week 8)
- Full testing completed
- Bug fixes implemented
- Documentation finalized
- System deployed and live


### 6.4 Risk Management
The implementation plan includes 1-week buffer time distributed across the development phases to accommodate potential delays or unexpected challenges. Weekly progress reviews will be conducted to ensure the project remains on schedule.