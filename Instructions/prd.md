```markdown
# QR Review System PRD

**Version:** 1.0
**Date:** May 20, 2025

## 1. Executive Summary

The QR Review System is a web-based software designed to empower Small and Medium Businesses (SMBs) to efficiently collect direct customer feedback. By leveraging unique QR codes placed at physical locations or shared digitally, vendors can direct customers to a simple, mobile-optimized review page. The system facilitates the collection of star ratings and comments, prompts for specific feedback on lower ratings, provides vendors with a dashboard to manage and analyze reviews, and integrates with platforms like Google Reviews to help manage online reputation. The goal is to provide SMBs with actionable customer insights and streamline the process of gathering reviews.

## 2. Product Vision

Our vision is to become the leading direct feedback platform for SMBs, making it effortless for them to understand their customers and improve their services. We aim to replace outdated feedback methods with a simple, modern, and integrated solution.

*   **Purpose:** To provide SMBs with a direct, controlled, and actionable channel for collecting customer feedback, fostering customer loyalty, and enhancing online reputation.
*   **Target Users:**
    *   **Small/Medium Business Owners (Vendors):** Individuals or teams running businesses (restaurants, retail stores, service providers, etc.) who need to gather customer feedback efficiently, understand pain points, improve service, and boost their online presence (e.g., Google Reviews). They value ease of use, actionable data, and integrations that save them time.
    *   **Customers (End-users):** Individuals who have interacted with an SMB and are willing to provide feedback quickly and conveniently, typically via their mobile device. They value a simple, fast process that feels like their feedback is heard.
*   **Business Goals:**
    *   Acquire a significant base of paying SMB subscribers across various tiers within the first year.
    *   Achieve high vendor retention rates by demonstrating clear value through actionable insights and time-saving integrations.
    *   Establish the product as a necessary tool for local businesses focused on customer experience and online reputation.
    *   Generate recurring revenue through a tiered subscription model.

## 3. User Personas

**Persona 1: Sarah, The Restaurant Owner**

*   **Background:** Sarah owns a busy local cafe and bakery. She's passionate about her food and service but struggles to get consistent customer feedback. She sees generic comment cards ignored and only hears complaints when customers are very unhappy or leave a public negative review online, which is hard to manage. She's comfortable with technology but has limited time to learn complex systems.
*   **Goals:**
    *   Easily collect feedback from *all* types of customers (happy and unhappy).
    *   Understand *why* customers are leaving lower ratings so she can make specific improvements.
    *   Encourage happy customers to leave positive reviews on Google to attract more business.
    *   Have a central place to see all feedback and respond quickly if needed.
    *   Spend less time trying to chase reviews or sift through scattered feedback.
*   **Pain Points:**
    *   Lack of time to implement and manage complex feedback systems.
    *   Negative online reviews feeling public and damaging with no easy way to understand the root cause or respond effectively.
    *   Difficulty prompting happy customers to leave reviews on specific platforms like Google.
    *   Not knowing *what* specifically to improve based on general feedback.

**Persona 2: David, The Customer**

*   **Background:** David is a regular consumer who uses his smartphone for everything. He appreciates good service and is sometimes willing to leave feedback, but he's busy and easily frustrated by clunky processes, long forms, or needing to download an app. He wants to provide feedback quickly while he's thinking about it.
*   **Goals:**
    *   Provide feedback quickly and easily using his phone.
    *   Feel like his feedback is being considered by the business.
    *   Be able to quickly express satisfaction or dissatisfaction without hassle.
*   **Pain Points:**
    *   Review processes that require signing up, downloading apps, or typing on tiny forms.
    *   Feeling like his feedback goes into a void.
    *   Difficulty finding the right place to leave a review (especially positive ones) on platforms like Google or Yelp while he's in the moment.

## 4. Feature Specifications

This section details the requirements for each core feature.

### Feature: QR Code Generation

*   **Description:** Allows vendors to create unique QR codes linked to their mobile review page for specific locations or purposes (e.g., table number, server name - *initial version might not support this granularity*).
*   **User Stories:**
    *   As a vendor, I want to generate a unique QR code for my business location so that customers can easily access my review page.
    *   As a vendor, I want to download my generated QR code in a high-quality format (e.g., PNG, SVG) so that I can print it for display.
    *   As a vendor, I want to see a list of the QR codes I have generated so that I can manage them.
*   **Acceptance Criteria:**
    *   The system must allow vendors to generate a unique QR code linked to their specific account/location's review page URL.
    *   Generated QR codes must be downloadable in at least PNG format. SVG format should also be available for scalability.
    *   QR codes must be scannable by standard mobile QR code readers.
    *   Generated QRs must accurately direct users to the associated mobile review page URL.
    *   The vendor dashboard must display a list of generated QR codes, showing creation date and linking to the review page.
*   **Edge Cases:**
    *   Vendor has no locations added (should prevent QR generation).
    *   Generating a large number of QR codes.
    *   Download failure due to network issues.
    *   QR code linked to a location that is later deleted or disabled (QR should ideally redirect to a "page not found" or generic business page).

### Feature: Mobile Review Page

*   **Description:** A simple, mobile-first web page where customers land after scanning a QR code or clicking a review link, allowing them to submit a review.
*   **User Stories:**
    *   As a customer, I want to quickly rate the business (e.g., 1-5 stars) on my mobile phone.
    *   As a customer, I want to leave optional text comments about my experience.
    *   As a customer, I want the page to load quickly and be easy to use on any smartphone.
    *   As a customer, I want to see a confirmation message after submitting my review.
*   **Acceptance Criteria:**
    *   The page must be fully responsive and optimized for mobile browsers.
    *   The page must display the business name/logo (linked from vendor account).
    *   Must include a prominent star rating input (e.g., 1-5 stars).
    *   Must include a text area for optional comments.
    *   Must have a clear "Submit Review" button.
    *   Upon successful submission, a confirmation message (e.g., "Thank you for your feedback!") must be displayed.
    *   Input fields should be cleared after submission.
    *   Basic validation (e.g., rating is required, comments optional).
*   **Edge Cases:**
    *   No network connection on the customer's phone during submission (handle gracefully, maybe suggest trying again).
    *   Invalid or expired QR code leading to the page (display error message).
    *   User tries to submit extremely long comments (limit character count).
    *   Page accessed via direct URL without a QR code parameter (should still work, maybe without specific location context initially).
    *   Security: Prevent malicious script injection in comments.

### Feature: Conditional Feedback

*   **Description:** If a customer submits a low rating (configurable, but default < 5 stars), additional specific feedback questions are displayed.
*   **User Stories:**
    *   As a customer who left a low rating, I want to be asked specific questions about my experience so I can provide more detail.
    *   As a vendor, I want customers who leave low ratings to be prompted for specific feedback so I can understand the issues better.
*   **Acceptance Criteria:**
    *   When a customer selects a rating below 5 stars (default threshold, potentially vendor configurable in future), an additional section of questions/prompts appears *before* or *after* they hit submit.
    *   The specific questions should be pre-defined (e.g., "What could we improve?"). (Future: Allow vendor customization of questions).
    *   The customer should be able to fill out these additional fields (text area).
    *   The detailed feedback submitted must be linked to the specific review in the vendor dashboard.
*   **Edge Cases:**
    *   User initially selects <5 stars, sees questions, then changes rating to 5 stars (questions should hide).
    *   User leaves <5 stars but chooses not to fill out the conditional feedback fields (should still allow submitting the core review).
    *   Error saving the detailed feedback while the core review saves (log error, save what was possible).

### Feature: Vendor Dashboard

*   **Description:** A secure web interface for vendors to view, manage, filter, and respond to submitted reviews.
*   **User Stories:**
    *   As a vendor, I want to securely log in to my dashboard.
    *   As a vendor, I want to see a list of all customer reviews received.
    *   As a vendor, I want to filter reviews by date, rating, and location.
    *   As a vendor, I want to read the full comment and any conditional feedback for each review.
    *   As a vendor, I want to respond to customer reviews directly from the dashboard.
    *   As a vendor, I want to mark reviews as addressed or responded to.
*   **Acceptance Criteria:**
    *   Requires secure user authentication for vendor access.
    *   Displays a paginated list of reviews received by the vendor's account.
    *   Each review entry must show the rating, truncated comment (with option to expand), timestamp, and source (e.g., QR code scan from Location X).
    *   Includes filtering options for date range, minimum/maximum star rating, and location.
    *   Clicking on a review must display full details, including the main comment and any conditional feedback.
    *   A text area must be available to write a response to a review.
    *   Responses must be saved and associated with the review.
    *   Status indicators (e.g., "Pending Response", "Responded") should be visible.
    *   Responses should be visible to the vendor on the dashboard, associated with the original review (responses are internal to the system initially, not visible to the customer who left the review unless a future feature adds this).
*   **Edge Cases:**
    *   No reviews received yet (display an empty state message).
    *   Very large volume of reviews requiring efficient pagination/loading.
    *   Filter combinations resulting in no results.
    *   Multiple vendors logged in simultaneously for the same account (less likely with SMBs but possible).
    *   Error submitting a response (display error message).

### Feature: Analytics

*   **Description:** Provides vendors with visual reports and trends based on collected review data.
*   **User Stories:**
    *   As a vendor, I want to see my average star rating over time.
    *   As a vendor, I want to see the distribution of ratings (e.g., how many 5-star, 4-star, etc. reviews).
    *   As a vendor, I want to see the number of reviews received per day/week/month.
    *   As a vendor, I want to filter analytics data by date range or location.
*   **Acceptance Criteria:**
    *   Dashboard section dedicated to Analytics.
    *   Display the overall average star rating.
    *   Visualize the distribution of ratings (e.g., bar chart or pie chart showing counts/percentages for each star level).
    *   Visualize review volume over time (e.g., line or bar chart showing number of reviews per period).
    *   Provide filtering options consistent with the review list (date range, location).
    *   Analytics data must update dynamically based on applied filters.
*   **Edge Cases:**
    *   No data available for the selected period/filters.
    *   Only a few reviews, making trends less meaningful (display raw numbers instead of charts maybe).
    *   Calculating average for zero reviews (should display N/A or 0).

### Feature: Google Reviews Integration

*   **Description:** Facilitates prompting happy customers (5-star reviewers) to also leave a review on the vendor's Google My Business (GMB) page by providing a direct link generated via the GMB API.
*   **User Stories:**
    *   As a vendor, I want to connect my Google My Business account to the system.
    *   As a vendor, I want to select which of my GMB locations correspond to my business locations in the system.
    *   As a customer who left a 5-star review, I want to be easily directed to leave a review on the business's Google page if I choose to.
*   **Acceptance Criteria:**
    *   Provide a clear process for vendors to authenticate and connect their Google account (using OAuth 2.0) and authorize access to their GMB data.
    *   Allow vendors to fetch their list of GMB locations via the API.
    *   Provide an interface for vendors to map their internal business locations to specific GMB locations.
    *   After a customer successfully submits a 5-star review, display a clear, optional prompt (e.g., "Liked your experience? Please also leave us a review on Google!") with a button/link.
    *   This link must be dynamically generated using the Google My Business API to direct the user directly to the review submission form for the mapped GMB location.
    *   Log success/failure of GMB API requests (e.g., fetching locations, generating links).
*   **Edge Cases:**
    *   Vendor does not have a GMB listing.
    *   GMB API is temporarily down or returns an error.
    *   Vendor disconnects their Google account after setup.
    *   Mapping errors (vendor maps to the wrong GMB location).
    *   Customer closes the page before seeing/clicking the Google prompt.
    *   Google changes its API or policies regarding review links.

### Feature: Custom Email Templates

*   **Description:** Allows vendors to create, save, and send branded email requests for reviews to customer lists, tracking basic engagement.
*   **User Stories:**
    *   As a vendor, I want to create and save email templates with my branding and custom message to request reviews.
    *   As a vendor, I want to include a link in the email that directs customers to my mobile review page.
    *   As a vendor, I want to send these emails to a list of customer email addresses (via manual upload or integration).
    *   As a vendor, I want to see basic tracking (e.g., number sent, opened, clicked) for emails sent.
*   **Acceptance Criteria:**
    *   Provide a WYSIWYG or rich text editor for creating email content.
    *   Allow saving multiple email templates.
    *   Include a dynamic placeholder/button that inserts the correct link to the vendor's mobile review page (similar to the QR code link).
    *   Provide a mechanism to send emails (initial version: manual upload of CSV with email addresses; future: integrate with CRM).
    *   Integrate with an email sending service (e.g., SendGrid, Postmark) for reliable delivery.
    *   Basic tracking data (sent, opened, clicked) should be recorded and visible to the vendor for each campaign/send.
*   **Edge Cases:**
    *   Invalid email addresses in the uploaded list (should skip or report errors).
    *   High bounce rates or spam complaints affecting sender reputation.
    *   Email sending service API errors.
    *   Tracking limitations (open/click tracking isn't 100% reliable due to user settings, privacy tools).
    *   Vendor uploads a massive list of emails exceeding plan limits or system capacity.
    *   Vendor uses inappropriate or spammy content.

### Feature: Public Landing Page

*   **Description:** A public-facing website showcasing the product's features, benefits, pricing plans, and a call to action for new users to sign up.
*   **User Stories:**
    *   As a prospective user, I want to understand what the QR Review System is and how it can help my business.
    *   As a prospective user, I want to see the different features offered.
    *   As a prospective user, I want to compare the pricing plans (Free, Starter, Pro, Enterprise).
    *   As a prospective user, I want to easily sign up for a plan.
    *   As a prospective user, I want the website to look professional and be easy to navigate on any device.
*   **Acceptance Criteria:**
    *   Clearly communicate the product's value proposition on the homepage.
    *   Dedicated sections explaining key features and benefits.
    *   A clear, comparative pricing table outlining features and limits for each plan (Free, Starter, Pro, Enterprise).
    *   Prominent "Sign Up" or "Get Started" Call-to-Action buttons.
    *   Links to relevant sections (Features, Pricing, Contact, etc.).
    *   Fully responsive design for desktop, tablet, and mobile.
    *   Secure HTTPS connection.
*   **Edge Cases:**
    *   Page loading errors.
    *   Broken links or images.
    *   Pricing information is out of date or unclear.
    *   Signup form submission fails.

### Feature: Subscription Management

*   **Description:** Defines and manages the different tiered plans, mapping features and limits to each plan, and controlling user access based on their active subscription.
*   **User Stories:**
    *   As a new user, I want to select a subscription plan when I sign up (or default to Free).
    *   As a vendor, I want my account access and feature availability to match my current subscription plan.
    *   As a vendor, I want to see what plan I am on and what my limits are.
    *   As a vendor, I want to be able to upgrade or downgrade my plan.
    *   As a vendor, I want to be notified if I am approaching or have hit plan limits (e.g., number of locations, QRs generated).
    *   As a vendor, I want to be able to cancel my subscription.
*   **Acceptance Criteria:**
    *   Define the structure and permissions for Free, Starter, Pro, and Enterprise plans.
    *   Map specific features and quantitative limits (e.g., max locations, max QR codes, analytics history depth, email sends per month, GMB integrations) to each plan.
    *   During the signup process, allow selection of a plan (or automatic assignment to Free).
    *   The system must enforce plan limits before allowing restricted actions (e.g., prevent adding a 2nd location on a Free plan).
    *   The vendor dashboard must clearly display the current plan and usage against limits.
    *   Provide an interface for vendors to initiate plan upgrades or downgrades.
    *   Implement logic for handling plan changes and associated billing adjustments (prorating - V1 might be simpler, e.g., change takes effect next cycle).
    *   Provide a clear process for subscription cancellation.
    *   Implement notifications for users approaching or exceeding key plan limits.
*   **Edge Cases:**
    *   User tries to access a feature not included in their plan.
    *   Plan change fails during processing.
    *   User cancels but continues to use features beyond their access (should be blocked).
    *   Handling suspended accounts due to payment failure.
    *   Edge case limits: What happens precisely when a user hits the max QRs? (Disable generation button, display message).

### Feature: Payment Processing

*   **Description:** Integrates with Stripe and PayPal to handle recurring billing for paid subscription plans.
*   **User Stories:**
    *   As a user upgrading to a paid plan, I want to securely enter my payment information (credit card or PayPal).
    *   As a user on a paid plan, I want my subscription to be automatically renewed and billed periodically (e.g., monthly/annually).
    *   As a vendor, I want confirmation when my payment is successful.
    *   As a vendor, I want to be notified if a payment fails.
    *   As a vendor, I want to manage my payment methods (update card) (Future refinement).
*   **Acceptance Criteria:**
    *   Integrate with Stripe using their recommended client-side (Stripe Elements) and server-side APIs for collecting payment details securely without sensitive data touching our servers directly.
    *   Integrate with PayPal for processing recurring subscription payments.
    *   Create and manage recurring subscriptions for users via Stripe and PayPal APIs.
    *   Handle successful payment confirmations from both providers.
    *   Implement webhook handlers for Stripe and PayPal to receive notifications about payment success, failure, disputes, etc.
    *   Associate successful payments with the user's subscription record in our system.
    *   Implement a retry logic for failed payments (handled partly by payment providers, partly by our webhook processing).
    *   Notify the user via email or dashboard message about payment success or failure.
*   **Edge Cases:**
    *   Payment provider API is unresponsive or returns errors.
    *   User's payment method is declined.
    *   Chargebacks or disputes (requires manual handling initially, potentially).
    *   Mismatch between subscription status in our database and the payment provider's system (needs robust webhook processing and reconciliation logic).
    *   Handling different currencies (if applicable - assume one currency for V1).
    *   User attempts to use a payment method not supported by their chosen provider.

## 5. Technical Requirements

*   **Platform Architecture:** Web application built using the Laravel framework (PHP) for the backend and potentially Vue/React or server-side rendering for the frontend. Supabase is recommended as the database (PostgreSQL) and potentially for authentication/storage features, although Laravel's built-in features or alternatives like AWS RDS/S3 are also options.
*   **Database:** Relational database (PostgreSQL as suggested by Supabase). Key tables will include `users`, `vendors` (linking to users), `locations`, `qrcodes`, `reviews`, `review_responses`, `email_templates`, `subscriptions`, `payments`, `plan_features`. Data storage must comply with relevant privacy regulations (e.g., GDPR if applicable).
*   **APIs & Integrations:**
    *   Google My Business API: For fetching location details and generating review links for 5-star reviews. Requires OAuth 2.0 for vendor authentication.
    *   Stripe API: For secure collection of payment details, creating subscriptions, managing billing, and handling webhooks.
    *   PayPal API: For creating subscriptions, managing billing, and handling webhooks.
    *   Email Sending Service API (e.g., SendGrid, Postmark, AWS SES): For sending custom review request emails and transactional emails (payment notifications, etc.).
    *   Potential Future: Integration with CRMs or POS systems for automated email list syncing.
*   **Security:**
    *   User Authentication & Authorization: Secure vendor login, role-based access control (vendor vs. admin - admin not specified in prompt, but good to consider), enforcing plan limits.
    *   Data Security: Encrypt sensitive data at rest and in transit. Protect review data privacy. Secure handling of payment information (rely on Stripe/PayPal SDKs).
    *   QR Code Security: Ensure uniqueness; while QRs aren't inherently secure, the *linked page* must be secure and handle potentially malicious query parameters gracefully. Prevent QR code spoofing where possible (e.g., by including unique, non-guessable IDs).
*   **Scalability:** Design database schema and application architecture with scalability in mind. Use appropriate caching mechanisms if performance becomes an issue. Consider potential for high traffic on review pages and large volumes of review data.
*   **Hosting:** Cloud-based hosting platform (e.g., AWS, GCP, DigitalOcean) for the web application and database.

## 6. Implementation Roadmap

Given the 2-month timeline, a phased approach is necessary to deliver core value early and layer on integrations and advanced features.

**Phase 1: Core Feedback Loop & Foundation (~4-5 weeks)**

*   **Infrastructure Setup:**
    *   Set up development, staging, and production environments.
    *   Initialize Laravel project and connect to Supabase (or chosen DB).
    *   Implement basic user authentication (vendor signup/login).
*   **Public Landing Page & Signup:**
    *   Develop the static content for the landing page.
    *   Implement the basic signup flow.
*   **Core Review Functionality:**
    *   Database schema design for `locations`, `qrcodes`, `reviews`.
    *   Implement vendor functionality to add/manage locations (simple list).
    *   Implement QR Code Generation (unique link, basic PNG download).
    *   Develop the Mobile Review Page (rating, comment, submission).
    *   Implement storing submitted reviews in the database, linked to location/QR.
*   **Vendor Dashboard (Basic):**
    *   Implement secure vendor login to dashboard.
    *   Display a simple list of all received reviews (no filtering/response yet).
*   **Conditional Feedback (Basic):**
    *   Implement the logic to display the conditional feedback field for <5 star ratings.
    *   Save the conditional feedback text with the review.
*   **Subscription Management (Basic):**
    *   Define plan structure in the database/config.
    *   Assign 'Free' plan on signup.
    *   Gate *adding* a location or *generating* a QR code based on a simple 'Free' vs 'Paid' check (initial placeholder for limits).

**Phase 2: Enhancements, Integrations & Paid Features (~4-5 weeks)**

*   **Vendor Dashboard (Advanced):**
    *   Implement filtering for reviews (date, rating, location).
    *   Implement review response functionality (internal).
    *   Add review detail view.
*   **Analytics:**
    *   Implement queries and visualizations for average rating, distribution, and volume over time.
    *   Integrate filters with analytics views.
*   **Google Reviews Integration:**
    *   Implement Google OAuth flow.
    *   Implement fetching GMB locations.
    *   Implement location mapping interface.
    *   Implement GMB API call to generate review link for 5-star reviews.
    *   Modify Mobile Review Page to display Google prompt after 5-star submission.
*   **Custom Email Templates:**
    *   Implement Email Template editor (WYSIWYG).
    *   Implement saving/managing templates.
    *   Integrate with Email Sending Service API.
    *   Implement basic email sending functionality (e.g., single send or small list upload for V1).
    *   Implement basic tracking (sent). Open/click tracking might be a v1.1 item depending on complexity.
*   **Subscription Management & Payment Processing:**
    *   Implement full plan logic with defined limits (locations, QRs, analytics history, etc.).
    *   Implement plan upgrade flow.
    *   Integrate Stripe for subscription billing.
    *   Implement Stripe webhook handling for subscription lifecycle.
    *   Implement limit enforcement throughout the application.
    *   *PayPal Integration and Downgrade/Cancellation might be pushed to V1.1 if Stripe proves complex.* Focus on getting *one* payment method and *upgrade* path working first.
*   **Refinement & Testing:**
    *   Extensive testing across all features, focusing on mobile responsiveness and edge cases identified.
    *   Performance optimization.
    *   Security audits.
    *   Documentation for vendors.

**Post-Launch (V1.1+):**

*   PayPal integration if not completed in V1.
*   Subscription downgrade and cancellation logic refinement (e.g., prorating).
*   Advanced analytics (trends, sentiment analysis).
*   More granular QR code linking (e.g., per table, per staff).
*   Public display of vendor responses.
*   Allow vendor customization of conditional feedback questions.
*   Integrations with popular POS or CRM systems for customer lists.
*   Email list upload for email templates.
*   SMS review requests.
*   Review widgets for vendor websites.

This roadmap prioritizes the core feedback loop and the most critical features necessary for a viable product offering within the tight 2-month timeframe, pushing some of the less critical or more complex integrations/features to a subsequent release.
```
