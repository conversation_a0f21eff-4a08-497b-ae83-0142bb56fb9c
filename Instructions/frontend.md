```markdown
# Frontend Implementation Guide: QR Review System

**Version: 1.0**
**Date: May 20, 2025**

This document outlines the frontend implementation strategy for the QR Review System, covering core architecture, state management, UI considerations, API integration, testing, and providing practical code examples.

## 1. Component Architecture

The application should adopt a modular component architecture, separating UI elements, pages, layouts, and state management logic. A component-based framework like React, Vue.js, or Angular is recommended. Given the Laravel/Supabase backend recommendation, React (with Next.js or Vite) or Vue.js (with Nuxt 3 or Vite) are strong candidates for ease of integration and community support.

A potential component structure could look like this:

```
src/
├── components/          // Reusable UI components (buttons, inputs, modals, etc.)
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Modal.tsx
│   │   └── LoadingSpinner.tsx
│   ├── vendor/
│   │   ├── ReviewItem.tsx
│   │   ├── QRCard.tsx
│   │   ├── AnalyticsChart.tsx
│   │   ├── EmailTemplateEditor.tsx
│   │   └── ...
│   └── customer/
│       └── StarRating.tsx
│       └── ConditionalFeedbackForm.tsx
│       └── ...
├── layouts/             // Application layouts (public, authenticated vendor)
│   ├── PublicLayout.tsx
│   └── VendorLayout.tsx
├── pages/               // Route-specific components
│   ├── public/
│   │   ├── IndexPage.tsx      // Landing page
│   │   ├── PricingPage.tsx
│   │   ├── LoginPage.tsx
│   │   └── RegisterPage.tsx
│   ├── vendor/
│   │   ├── DashboardPage.tsx
│   │   ├── QRManagementPage.tsx
│   │   ├── ReviewsPage.tsx
│   │   ├── AnalyticsPage.tsx
│   │   ├── IntegrationsPage.tsx // Google Reviews, Email
│   │   ├── SettingsPage.tsx     // Subscription, Profile
│   │   └── ...
│   └── review/[qrCodeId].tsx // Mobile Review Page (Dynamic route)
├── hooks/               // Custom hooks (data fetching, auth, etc.)
│   ├── useAuth.ts
│   ├── useApi.ts
│   └── ...
├── contexts/            // React Context for global state (if using Context API)
│   └── AuthContext.tsx
├── stores/              // State management store (e.g., Zustand, Pinia)
│   └── useAppStore.ts
├── utils/               // Utility functions (date formatting, validation, etc.)
│   └── api.ts           // API client setup
│   └── validation.ts
└── App.tsx / main.tsx   // App entry point and routing setup
```

**Relationships:**

*   `App` handles routing and potentially provides global contexts/stores.
*   `layouts` wrap `pages`, providing consistent navigation, headers, footers, etc.
*   `pages` are responsible for fetching data (via hooks/API utils) and orchestrating `components` to display UI.
*   `components` are the building blocks, receiving data via props and emitting events/callbacks.
*   `hooks`, `contexts`, `stores`, and `utils` provide reusable logic and state management.

## 2. State Management

Effective state management is crucial for handling user authentication, vendor data, review lists, form states, loading indicators, and errors across different parts of the application.

Recommended approach:

1.  **Server State (Data Fetching):** Use a dedicated library like **React Query (or TanStack Query)** or **SWR**. These libraries excel at managing asynchronous data fetching, caching, synchronization, background updates, and handling loading/error states automatically. This is ideal for lists of reviews, QR codes, analytics data, etc.
2.  **Global Client State:** For non-server state that needs to be shared across distant components (e.g., current authenticated user details, subscription status, notification messages), use a lightweight global store like **Zustand** (for React) or **Pinia** (for Vue). Alternatively, the **Context API** (React) is suitable for moderate state sharing.
3.  **Component State:** For simple UI state that is local to a single component (e.g., form input values, modal open/closed state, dropdown visibility), use the framework's built-in state management (e.g., `useState` in React, `ref`/`reactive` in Vue).

**Example:** Using Zustand for user state and React Query for fetching reviews.

```typescript
// stores/useAppStore.ts (Zustand)
import { create } from 'zustand';

interface UserState {
  user: { id: string; email: string; plan: string } | null;
  isAuthenticated: boolean;
  isLoadingAuth: boolean;
  login: (userData: any) => void;
  logout: () => void;
  setLoadingAuth: (loading: boolean) => void;
}

export const useAppStore = create<UserState>((set) => ({
  user: null,
  isAuthenticated: false,
  isLoadingAuth: true, // Assume loading initially to check auth status
  login: (userData) => set({ user: userData, isAuthenticated: true, isLoadingAuth: false }),
  logout: () => set({ user: null, isAuthenticated: false, isLoadingAuth: false }),
  setLoadingAuth: (loading) => set({ isLoadingAuth: loading }),
}));

// hooks/useReviews.ts (React Query)
import { useQuery } from '@tanstack/react-query';
import api from '../utils/api'; // Your configured API client

const fetchReviews = async (vendorId: string) => {
  const { data } = await api.get(`/vendors/${vendorId}/reviews`);
  return data;
};

export const useReviews = (vendorId: string | null) => {
  return useQuery({
    queryKey: ['reviews', vendorId], // Unique key for caching
    queryFn: () => fetchReviews(vendorId!),
    enabled: !!vendorId, // Only fetch if vendorId is available
  });
};

// pages/vendor/ReviewsPage.tsx (Using the hooks)
import React from 'react';
import { useAppStore } from '../../stores/useAppStore';
import { useReviews } from '../../hooks/useReviews';
import ReviewItem from '../../components/vendor/ReviewItem';
import LoadingSpinner from '../../components/common/LoadingSpinner';

function ReviewsPage() {
  const { user } = useAppStore();
  const vendorId = user?.id || null; // Get vendor ID from global store

  const { data: reviews, isLoading, error } = useReviews(vendorId);

  if (isLoading) return <LoadingSpinner />;
  if (error) return <div>Error loading reviews: {error.message}</div>;

  return (
    <div>
      <h2>Customer Reviews</h2>
      {reviews.length === 0 ? (
        <p>No reviews yet.</p>
      ) : (
        <ul>
          {reviews.map((review: any) => (
            <ReviewItem key={review.id} review={review} />
          ))}
        </ul>
      )}
    </div>
  );
}

export default ReviewsPage;
```

## 3. UI Design

Key considerations for the User Interface:

*   **Mobile-First Responsiveness:** Crucial for the customer-facing Review Page. Use CSS Flexbox/Grid and media queries. Design the Review Page layout specifically for small screens.
*   **Vendor Dashboard Layout:**
    *   Clear, consistent navigation (sidebar recommended for desktop, collapsed/drawer for mobile).
    *   Main content area that adapts to screen size.
    *   Data tables for reviews, QR codes: Implement pagination, sorting, and filtering. Consider libraries like TanStack Table.
    *   Use cards or distinct sections to organize information on the dashboard overview.
    *   Forms (QR generation, email template editor): Clear labels, validation feedback, intuitive input fields.
*   **Review Page:**
    *   Minimalist design, focused on the task.
    *   Prominent star rating input.
    *   Conditional comment box/detailed feedback form (shown only for < 5 stars).
    *   Clear "Submit" button.
    *   Optionally include vendor branding (logo, colors).
    *   Handle loading and success/error states clearly after submission.
*   **Public Landing Page:** Engaging design showcasing features, benefits, and clear calls to action for signing up/viewing pricing.
*   **Consistency:** Use a UI component library (Material UI, Ant Design, Chakra UI, Tailwind CSS + a component library like Headless UI/Radix UI) for a consistent look and feel and faster development.
*   **Accessibility:** Design with accessibility in mind (keyboard navigation, ARIA attributes, sufficient color contrast, alt text for images).
*   **User Feedback:** Provide visual feedback for actions (button loading states, success/error notifications/toasts, form validation errors).

## 4. API Integration

The frontend will communicate with the Laravel/Supabase backend via API calls.

*   **RESTful API:** Assume a RESTful API structure (e.g., `/api/vendors`, `/api/reviews`, `/api/qrs`).
*   **API Client:** Use `fetch` API or a library like Axios for making HTTP requests. Configure a base API client with default headers (like `Content-Type: application/json`).
*   **Authentication:** Implement token-based authentication (e.g., JWT or OAuth tokens) or session-based authentication depending on the backend strategy. Include the authentication token in the `Authorization` header for protected routes.
*   **Handling Responses:**
    *   Always check for HTTP status codes (e.g., 200 OK, 201 Created, 400 Bad Request, 401 Unauthorized, 404 Not Found, 500 Internal Server Error).
    *   Parse JSON responses.
    *   Implement error handling (displaying user-friendly messages for different error types, e.g., invalid input, unauthorized access, server error).
*   **Data Fetching & Mutation:**
    *   Use React Query/SWR for `GET` requests to handle caching, loading, and error states automatically.
    *   For `POST`, `PUT`, `DELETE` requests (mutations), use the mutation capabilities of these libraries to handle submission state and data invalidation/refetching.
*   **File Uploads:** For features like uploading a vendor logo or profile picture, use `FormData` with the `POST` request.

**Example: API Client and using it for a mutation**

```typescript
// utils/api.ts
import axios from 'axios';
import { useAppStore } from '../stores/useAppStore'; // Assuming you use a global store for token

const api = axios.create({
  baseURL: '/api', // Adjust if your API has a different base path
  headers: {
    'Content-Type': 'application/json',
  },
});

// Optional: Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const { user } = useAppStore.getState(); // Access state outside hook
    if (user && (user as any).token) { // Assuming token is part of user state
      config.headers['Authorization'] = `Bearer ${(user as any).token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Optional: Add response interceptor for handling global errors (e.g., 401)
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // Handle unauthorized: redirect to login, clear user state, etc.
      useAppStore.getState().logout();
      // Redirect logic here (e.g., window.location.href = '/login')
    }
    return Promise.reject(error);
  }
);

export default api;

// hooks/useGenerateQr.ts (React Query Mutation)
import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../utils/api';

interface GenerateQrPayload {
  name: string;
  // Add other necessary payload properties
}

const generateQrCode = async (payload: GenerateQrPayload) => {
  const { data } = await api.post('/vendor/qrs', payload);
  return data;
};

export const useGenerateQr = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateQrCode,
    onSuccess: (newQr) => {
      // Invalidate the 'qrs' query cache to refetch the list,
      // or manually update the cache if preferred
      queryClient.invalidateQueries({ queryKey: ['qrs'] });
      console.log('QR Code generated successfully:', newQr);
      // Show success message to user
    },
    onError: (error: any) => {
      console.error('Error generating QR Code:', error);
      // Show error message to user
    },
  });
};
```

## 5. Testing Approach

A comprehensive testing strategy is essential for delivering a stable application within a tight timeline.

*   **Unit Tests (Jest/Vitest + React Testing Library/Vue Test Utils):**
    *   Test individual components in isolation (props rendering correctly, event handlers triggering).
    *   Test utility functions and custom hooks.
    *   Focus on testing component *behavior* from a user's perspective, not internal implementation details.
*   **Integration Tests (React Testing Library/Vue Test Utils):**
    *   Test how components interact with each other.
    *   Test frontend logic that combines multiple units (e.g., a form component using a validation hook).
    *   Test frontend integration with *mocked* backend APIs to verify correct request payloads and response handling.
*   **End-to-End (E2E) Tests (Cypress/Playwright):**
    *   Test critical user flows simulating real user interaction in a browser.
    *   Examples: Vendor login -> Generate QR -> Scan QR (simulated) -> Submit Review -> Vendor Dashboard -> View Review.
    *   Test subscription flow via Stripe/PayPal (can be challenging, focus on happy paths and use test credentials).
    *   Focus on core business logic workflows.
*   **Manual Testing:**
    *   Essential for UI/UX nuances, responsiveness across various devices/browsers, and accessibility checks.
    *   Especially important for the customer Review Page on mobile devices.

**Testing Strategy:**

1.  Prioritize testing critical features: User authentication, QR generation, Review Submission (especially conditional logic), Vendor Dashboard review display/filtering, Subscription flow.
2.  Write unit tests for smaller, reusable components and pure functions.
3.  Write integration tests for page-level components that orchestrate multiple components and interact with the (mocked) API.
4.  Implement E2E tests for the most critical user journeys.
5.  Integrate testing into the CI/CD pipeline to prevent regressions.

## 6. Code Examples

Here are sample implementations for key frontend components, using React with TypeScript.

**6.1 Star Rating Component (Customer Review Page)**

```typescript
// components/customer/StarRating.tsx
import React, { useState } from 'react';

interface StarRatingProps {
  initialRating?: number;
  maxStars?: number;
  onRatingChange: (rating: number) => void;
}

const StarRating: React.FC<StarRatingProps> = ({
  initialRating = 0,
  maxStars = 5,
  onRatingChange,
}) => {
  const [rating, setRating] = useState(initialRating);
  const [hoverRating, setHoverRating] = useState(0);

  const handleStarClick = (starIndex: number) => {
    setRating(starIndex);
    onRatingChange(starIndex);
  };

  const handleStarHover = (starIndex: number) => {
    setHoverRating(starIndex);
  };

  const handleMouseLeave = () => {
    setHoverRating(0);
  };

  return (
    <div className="star-rating" onMouseLeave={handleMouseLeave}>
      {[...Array(maxStars)].map((_, index) => {
        const starValue = index + 1;
        const isFilled = starValue <= (hoverRating || rating);

        return (
          <span
            key={starValue}
            className={`star ${isFilled ? 'filled' : 'empty'}`}
            onClick={() => handleStarClick(starValue)}
            onMouseEnter={() => handleStarHover(starValue)}
            style={{
              cursor: 'pointer',
              fontSize: '24px',
              color: isFilled ? '#ffc107' : '#e4e5e9', // Standard star colors
              marginRight: '2px',
            }}
            aria-label={`${starValue} star${starValue > 1 ? 's' : ''}`}
            role="button"
          >
            &#9733; {/* Unicode star character */}
          </span>
        );
      })}
    </div>
  );
};

export default StarRating;
```

**6.2 Conditional Feedback Form Logic (Customer Review Page)**

This logic would be within the main Review Page component.

```typescript
// pages/review/[qrCodeId].tsx (Snippet)
import React, { useState } from 'react';
import StarRating from '../../components/customer/StarRating';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input'; // Assuming an Input component
import api from '../../utils/api'; // Your API client

function CustomerReviewPage() {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [detailedFeedback, setDetailedFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  // Assume qrCodeId is obtained from the route params

  const handleRatingChange = (newRating: number) => {
    setRating(newRating);
    // Clear detailed feedback if rating becomes 5 stars
    if (newRating === 5) {
      setDetailedFeedback('');
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (rating === 0) {
      alert('Please select a star rating.');
      return;
    }

    setIsSubmitting(true);
    setSubmissionStatus('idle');

    try {
      const reviewData = {
        qrCodeId: 'YOUR_QR_CODE_ID_HERE', // Get from route params
        rating: rating,
        comment: comment,
        detailedFeedback: rating < 5 ? detailedFeedback : null, // Only send if < 5 stars
      };

      // Replace with actual API call endpoint
      await api.post('/customer/reviews', reviewData);

      setSubmissionStatus('success');
      // Clear form or redirect
      setRating(0);
      setComment('');
      setDetailedFeedback('');

    } catch (error) {
      console.error('Review submission failed:', error);
      setSubmissionStatus('error');
      // Display error message to user
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="review-page-container">
      <h1>Leave a Review</h1>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>Your Rating:</label>
          <StarRating onRatingChange={handleRatingChange} />
          {rating > 0 && <p>You selected {rating} star{rating > 1 && 's'}.</p>}
        </div>

        <div className="form-group">
          <label htmlFor="comment">Comment (Optional):</label>
          <Input
            id="comment"
            type="text"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Share your general feedback..."
          />
        </div>

        {rating > 0 && rating < 5 && (
          <div className="form-group conditional-feedback">
            <p>We're sorry you didn't have a 5-star experience. Please tell us more:</p>
            <textarea
              id="detailedFeedback"
              value={detailedFeedback}
              onChange={(e) => setDetailedFeedback(e.target.value)}
              rows={4}
              placeholder="What could we improve?"
            />
          </div>
        )}

        <Button type="submit" disabled={rating === 0 || isSubmitting}>
          {isSubmitting ? 'Submitting...' : 'Submit Review'}
        </Button>

        {submissionStatus === 'success' && (
          <p className="success-message">Thank you for your feedback!</p>
        )}
        {submissionStatus === 'error' && (
          <p className="error-message">There was an error submitting your review. Please try again.</p>
        )}
      </form>
    </div>
  );
}
```

**6.3 Displaying a List of Reviews (Vendor Dashboard)**

Using the `useReviews` hook shown in the State Management section.

```typescript
// components/vendor/ReviewItem.tsx
import React from 'react';
import StarRatingDisplay from './StarRatingDisplay'; // A component to show stars, not input

interface ReviewItemProps {
  review: {
    id: string;
    rating: number;
    comment?: string;
    detailedFeedback?: string;
    customerIdentifier?: string; // e.g., truncated IP or unique ID
    submittedAt: string; // ISO date string
  };
}

const ReviewItem: React.FC<ReviewItemProps> = ({ review }) => {
  const submittedDate = new Date(review.submittedAt).toLocaleDateString(); // Format date

  return (
    <li className="review-item">
      <div className="review-header">
        <StarRatingDisplay rating={review.rating} />
        <span className="review-date">{submittedDate}</span>
      </div>
      {review.comment && <p className="review-comment">{review.comment}</p>}
      {review.detailedFeedback && (
        <div className="review-detailed-feedback">
          <p>Detailed Feedback:</p>
          <p>{review.detailedFeedback}</p>
        </div>
      )}
      <div className="review-meta">
        Submitted by: {review.customerIdentifier || 'Anonymous'}
        {/* Add vendor response functionality here */}
      </div>
    </li>
  );
};

export default ReviewItem;

// components/vendor/StarRatingDisplay.tsx (Helper component)
import React from 'react';

interface StarRatingDisplayProps {
  rating: number;
  maxStars?: number;
}

const StarRatingDisplay: React.FC<StarRatingDisplayProps> = ({ rating, maxStars = 5 }) => {
  return (
    <div className="star-rating-display">
      {[...Array(maxStars)].map((_, index) => {
        const starValue = index + 1;
        const isFilled = starValue <= rating;
        return (
          <span
            key={starValue}
            className={`star ${isFilled ? 'filled' : 'empty'}`}
            style={{ color: isFilled ? '#ffc107' : '#e4e5e9' }}
            aria-hidden="true" // Hide from screen readers as parent div has label
          >
            &#9733;
          </span>
        );
      })}
       <span className="sr-only">{rating} out of {maxStars} stars</span> {/* Screen reader text */}
    </div>
  );
};


// pages/vendor/ReviewsPage.tsx (Main page component using useReviews and ReviewItem)
// (Already shown in State Management section, uses the ReviewItem component)
```

This guide provides a foundational structure and practical considerations for building the frontend of the QR Review System within the given constraints. Remember to refine components, error handling, and integrate actual API endpoints as development progresses.
```
