# Supabase Setup Instructions

To complete the integration of the RatingShield QR system with Supabase, please follow these steps to set up the database schema:

## 1. Log in to Supabase Dashboard

Go to [https://app.supabase.io/](https://app.supabase.io/) and log in to your account.

## 2. Select Your Project

Select the project that you've configured in your `.env` file:

```
SUPABASE_URL=https://cjgpfcbnxpcfpgxihsgb.supabase.co
```

## 3. Apply the Database Schema

1. In the Supabase dashboard, navigate to the **SQL Editor** section.
2. Create a new query.
3. Copy the entire contents of the `database/migrations/supabase_schema.sql` file from this project.
4. Paste the SQL into the SQL Editor.
5. Click **Run** to execute the SQL and create all the necessary tables, functions, and policies.

## 4. Verify the Schema

After running the SQL, verify that the following tables have been created:

- `users`
- `qr_codes`
- `reviews`
- `templates`

You can check this by going to the **Table Editor** section in the Supabase dashboard.

## 5. Test the Integration

Once the schema is applied, you can test the integration by:

1. Running the application locally
2. Logging in with a Supabase user account
3. Creating a QR code in the dashboard
4. Verifying that the QR code appears in the Supabase database

## Troubleshooting

If you encounter any issues with the schema application:

- Make sure you're using the service role key for administrative operations
- Check that the SQL syntax is compatible with PostgreSQL (which Supabase uses)
- Verify that the RLS (Row Level Security) policies are correctly applied

For more information, refer to the Supabase documentation on [Database](https://supabase.io/docs/guides/database) and [Authentication](https://supabase.io/docs/guides/auth).
