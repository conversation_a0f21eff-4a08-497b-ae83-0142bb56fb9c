```markdown
# Backend Implementation Guide: QR Review System

**Version: 1.0**
**Date: May 20, 2025**

This document outlines the backend implementation plan for the QR Review System, focusing on a Laravel backend with Supabase as the primary data store and potentially for authentication/edge functions.

## 1. API Design

The API will be RESTful, versioned (`/api/v1`), and utilize JSON for request/response payloads. Authentication will be token-based for vendor-specific endpoints. The customer review submission endpoint will be public.

**Base URL:** `/api/v1`

**Authentication Endpoints (Vendor):**

*   **`POST /auth/register`**
    *   **Description:** Vendor registration and initial business creation.
    *   **Payload:** `{ "name": "Business Name", "email": "<EMAIL>", "password": "securepassword", "password_confirmation": "securepassword" }`
    *   **Response:** `{ "user": { ... }, "business": { ... }, "token": "jwt_token" }`
    *   **Auth:** Public
*   **`POST /auth/login`**
    *   **Description:** Vendor login.
    *   **Payload:** `{ "email": "<EMAIL>", "password": "securepassword" }`
    *   **Response:** `{ "user": { ... }, "business": { ... }, "token": "jwt_token" }`
    *   **Auth:** Public
*   **`POST /auth/logout`**
    *   **Description:** Vendor logout (invalidate token).
    *   **Response:** `{ "message": "Logged out successfully" }`
    *   **Auth:** Vendor Auth

**Business Endpoints (Vendor):**

*   **`GET /business`**
    *   **Description:** Get details of the authenticated vendor's business.
    *   **Response:** `{ "business": { ... } }`
    *   **Auth:** Vendor Auth
*   **`PUT /business`**
    *   **Description:** Update business details.
    *   **Payload:** `{ "name": "New Business Name", "address": "...", ... }`
    *   **Response:** `{ "business": { ... } }`
    *   **Auth:** Vendor Auth

**QR Code Endpoints (Vendor):**

*   **`GET /qrcodes`**
    *   **Description:** List QR codes for the authenticated business.
    *   **Params:** `page`, `limit`, `search` (optional filtering)
    *   **Response:** `{ "data": [ { ...qrCode } ], "meta": { pagination } }`
    *   **Auth:** Vendor Auth
*   **`POST /qrcodes`**
    *   **Description:** Generate a new QR code.
    *   **Payload:** `{ "name": "Entrance QR", "location_details": "Near the door" }`
    *   **Response:** `{ "qrcode": { ... } }`
    *   **Auth:** Vendor Auth
*   **`GET /qrcodes/{uuid}/download`**
    *   **Description:** Download QR code image by its public UUID.
    *   **Response:** File download (image/png).
    *   **Auth:** Vendor Auth (download is vendor specific) - *Note:* The *scan* URL is public.

**Review Endpoints:**

*   **`POST /reviews/{qr_uuid}/submit`**
    *   **Description:** Submit a review via QR code. (Public endpoint)
    *   **Payload:** `{ "rating": 5, "comment": "Great service!", "customer_name": "Optional Name", "customer_email": "<EMAIL>" }`
    *   **Response:** `{ "message": "Review submitted successfully", "review": { ... } }` (Might return conditional prompt info for frontend if < 5 stars)
    *   **Auth:** Public
*   **`GET /reviews`**
    *   **Description:** List reviews for the authenticated business.
    *   **Params:** `page`, `limit`, `rating` (filter), `qr_code_id` (filter), `start_date`, `end_date`, `sort`
    *   **Response:** `{ "data": [ { ...review } ], "meta": { pagination } }`
    *   **Auth:** Vendor Auth
*   **`GET /reviews/{id}`**
    *   **Description:** Get a specific review detail.
    *   **Response:** `{ "review": { ... } }`
    *   **Auth:** Vendor Auth
*   **`PUT /reviews/{id}/response`**
    *   **Description:** Add/Update vendor response to a review.
    *   **Payload:** `{ "response_text": "Thanks for your feedback!" }`
    *   **Response:** `{ "review": { ... } }`
    *   **Auth:** Vendor Auth

**Analytics Endpoints (Vendor):**

*   **`GET /analytics/summary`**
    *   **Description:** Get key analytics metrics (average rating, total reviews, etc.).
    *   **Params:** `start_date`, `end_date`, `qr_code_id` (optional filter)
    *   **Response:** `{ "summary": { ... } }`
    *   **Auth:** Vendor Auth
*   **`GET /analytics/reviews-over-time`**
    *   **Description:** Get review counts/average ratings over time.
    *   **Params:** `interval` (day, week, month), `start_date`, `end_date`, `qr_code_id` (optional filter)
    *   **Response:** `{ "data": [ { "date": "...", "count": N, "average_rating": N.N } ] }`
    *   **Auth:** Vendor Auth

**Subscription & Billing Endpoints:**

*   **`GET /plans`**
    *   **Description:** List available subscription plans.
    *   **Response:** `{ "data": [ { ...plan } ] }`
    *   **Auth:** Public/Vendor Auth
*   **`POST /subscription`**
    *   **Description:** Initiate subscription checkout (redirect to Stripe/PayPal).
    *   **Payload:** `{ "plan_id": 2, "payment_method": "stripe" }`
    *   **Response:** `{ "checkout_url": "..." }` or `{ "client_secret": "..." }` (depending on flow)
    *   **Auth:** Vendor Auth
*   **`GET /subscription`**
    *   **Description:** Get current subscription details for the business.
    *   **Response:** `{ "subscription": { ... } }`
    *   **Auth:** Vendor Auth
*   **`POST /webhooks/stripe`**
    *   **Description:** Stripe webhook endpoint (for events like `invoice.payment_succeeded`, `customer.subscription.deleted`).
    *   **Payload:** Stripe event object.
    *   **Auth:** Secured via Stripe signature verification.
*   **`POST /webhooks/paypal`**
    *   **Description:** PayPal webhook endpoint.
    *   **Payload:** PayPal event object.
    *   **Auth:** Secured via PayPal signature/verification.

**Email Template Endpoints (Vendor):**

*   **`GET /email-templates`**
    *   **Description:** List email templates for the business.
    *   **Response:** `{ "data": [ { ...template } ] }`
    *   **Auth:** Vendor Auth
*   **`POST /email-templates`**
    *   **Description:** Create a new email template.
    *   **Payload:** `{ "name": "Follow-up", "subject": "Thanks!", "body": "<p>Hi {{name}}, ...</p>" }`
    *   **Response:** `{ "template": { ... } }`
    *   **Auth:** Vendor Auth
*   **`PUT /email-templates/{id}`**
    *   **Description:** Update an email template.
    *   **Payload:** `{ "subject": "New Subject", ... }`
    *   **Response:** `{ "template": { ... } }`
    *   **Auth:** Vendor Auth
*   **`DELETE /email-templates/{id}`**
    *   **Description:** Delete an email template.
    *   **Response:** `{ "message": "Template deleted" }`
    *   **Auth:** Vendor Auth
*   **`POST /email-templates/{id}/send`**
    *   **Description:** Send an email using a template. (Triggered by vendor, maybe background job)
    *   **Payload:** `{ "recipients": ["<EMAIL>"], "data": { "name": "Customer Name" } }`
    *   **Response:** `{ "message": "Emails queued for sending" }`
    *   **Auth:** Vendor Auth

**Google Reviews Integration Endpoints (Vendor):**

*   **`POST /integrations/google/authorize`**
    *   **Description:** Initiate Google OAuth flow to link Google My Business account.
    *   **Response:** `{ "auth_url": "..." }` (URL to redirect vendor to Google)
    *   **Auth:** Vendor Auth
*   **`GET /integrations/google/callback`**
    *   **Description:** OAuth 2.0 callback endpoint (handles redirect from Google with code). Exchanges code for access/refresh tokens.
    *   **Params:** `code`, `state`
    *   **Auth:** Public (but should verify state parameter)
*   **`POST /integrations/google/submit-review`**
    *   **Description:** Trigger a workflow to encourage customer Google submission (e.g., generate a Google review link for a specific review). *Note: Direct API submission of customer reviews isn't standard. This likely means generating a link or notifying the vendor.*
    *   **Payload:** `{ "review_id": 123 }`
    *   **Response:** `{ "google_review_link": "..." }` (Optional: might redirect customer or show link to vendor)
    *   **Auth:** Vendor Auth

## 2. Data Models

Using a relational database structure suitable for Supabase.

*   **`users`**
    *   `id` (UUID - Supabase Auth ID, PK)
    *   `email` (string, unique)
    *   `password` (string - handled by Supabase Auth)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)
    *   `last_login_at` (timestamp, nullable)

*   **`businesses`**
    *   `id` (UUID, PK)
    *   `user_id` (UUID, FK to `users`, unique - one user per business initially)
    *   `name` (string)
    *   `address` (string, nullable)
    *   `phone` (string, nullable)
    *   `website` (string, nullable)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)
    *   `current_subscription_id` (UUID, FK to `subscriptions`, nullable)
    *   `google_place_id` (string, nullable - for Google Reviews integration)
    *   `google_access_token` (text, nullable - encrypted)
    *   `google_refresh_token` (text, nullable - encrypted)
    *   `google_token_expiry` (timestamp, nullable)

*   **`plans`**
    *   `id` (UUID, PK)
    *   `name` (string)
    *   `slug` (string, unique - e.g., 'free', 'starter', 'pro')
    *   `description` (text, nullable)
    *   `monthly_price` (numeric, nullable)
    *   `annual_price` (numeric, nullable)
    *   `features` (JSONB - e.g., `{ "max_qrcodes": 10, "analytics": true, "google_integration": false }`)
    *   `stripe_price_id_monthly` (string, nullable)
    *   `stripe_price_id_annual` (string, nullable)
    *   `paypal_plan_id_monthly` (string, nullable)
    *   `paypal_plan_id_annual` (string, nullable)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)

*   **`subscriptions`**
    *   `id` (UUID, PK)
    *   `business_id` (UUID, FK to `businesses`)
    *   `plan_id` (UUID, FK to `plans`)
    *   `status` (string - e.g., 'active', 'cancelled', 'past_due', 'trialing')
    *   `stripe_customer_id` (string, nullable)
    *   `stripe_subscription_id` (string, nullable)
    *   `paypal_agreement_id` (string, nullable)
    *   `current_period_end` (timestamp, nullable)
    *   `cancel_at_period_end` (boolean, default false)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)

*   **`qrcodes`**
    *   `id` (UUID, PK)
    *   `business_id` (UUID, FK to `businesses`)
    *   `uuid` (UUID, unique - public identifier)
    *   `name` (string)
    *   `location_details` (string, nullable)
    *   `generated_url` (string - the public review page URL)
    *   `scan_count` (integer, default 0)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)

*   **`reviews`**
    *   `id` (UUID, PK)
    *   `qr_code_id` (UUID, FK to `qrcodes`)
    *   `business_id` (UUID, FK to `businesses` - derived from qr_code_id, maybe denormalized for easier querying/RLS)
    *   `rating` (integer - 1 to 5)
    *   `comment` (text, nullable)
    *   `customer_name` (string, nullable)
    *   `customer_email` (string, nullable)
    *   `ip_address` (inet, nullable - for rate limiting/abuse prevention)
    *   `user_agent` (text, nullable)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)
    *   `submitted_to_google_at` (timestamp, nullable - records *when* we prompted/attempted Google submission, not confirmation it's live)
    *   `review_response_id` (UUID, FK to `review_responses`, nullable)

*   **`review_responses`**
    *   `id` (UUID, PK)
    *   `review_id` (UUID, FK to `reviews`, unique)
    *   `business_id` (UUID, FK to `businesses` - again, possibly denormalized)
    *   `response_text` (text)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)

*   **`email_templates`**
    *   `id` (UUID, PK)
    *   `business_id` (UUID, FK to `businesses`)
    *   `name` (string)
    *   `subject` (string)
    *   `body` (text - HTML or Markdown)
    *   `created_at` (timestamp)
    *   `updated_at` (timestamp)

*   **`email_sends`**
    *   `id` (UUID, PK)
    *   `business_id` (UUID, FK to `businesses`)
    *   `template_id` (UUID, FK to `email_templates`)
    *   `recipient_email` (string)
    *   `sent_at` (timestamp)
    *   `status` (string - e.g., 'queued', 'sending', 'sent', 'failed')
    *   `error_message` (text, nullable)
    *   `created_at` (timestamp)

**Supabase Considerations:**
*   Use built-in Auth for `users` table.
*   Implement Row Level Security (RLS) policies on `businesses`, `qrcodes`, `reviews`, `review_responses`, `email_templates`, `email_sends`, `subscriptions` tables to ensure vendors only access their data (e.g., `auth.uid() = user_id` or join through `businesses`).
*   Use `uuid` type for IDs.
*   Consider Edge Functions for specific lightweight tasks or webhooks, although Laravel's architecture might handle most logic.

## 3. Business Logic

**Key Flows:**

1.  **Vendor Onboarding:**
    *   User signs up (`/auth/register`).
    *   Create user in Supabase Auth.
    *   Create `businesses` record linked to `users.id`.
    *   Assign the default 'Free' plan by creating a `subscriptions` record.
    *   Generate API token (handled by Supabase Auth + Laravel session/JWT).
2.  **QR Code Management:**
    *   Vendor requests new QR code (`POST /qrcodes`).
    *   Check plan limits (`plans.features->max_qrcodes`).
    *   Generate a unique UUID (using Laravel's `Str::uuid()`).
    *   Construct the public review URL (`<APP_URL>/review/<uuid>`).
    *   Store a new `qrcodes` record (`uuid`, `business_id`, `generated_url`).
    *   Frontend uses the `generated_url` to display/download the QR image (e.g., using a QR generation library).
3.  **Customer Review Submission (`POST /reviews/{qr_uuid}/submit`):**
    *   Find `qrcode` by `uuid`.
    *   If not found, return 404.
    *   If found, get associated `business_id`.
    *   Basic validation: rating 1-5.
    *   Optional: Basic rate limiting by IP address.
    *   Store new `reviews` record (`qr_code_id`, `business_id`, `rating`, `comment`, `customer_name`, `customer_email`, `ip_address`, `user_agent`).
    *   Increment `scan_count` on the `qrcode`.
    *   *Conditional Feedback Logic (mostly Frontend):* Backend returns the saved review including the rating. Frontend reads the rating and conditionally displays a "Thank You" page vs. a "How can we improve?" prompt, possibly linking back to the backend if further input is needed (though simple comment field is sufficient per spec).
    *   *Google Reviews Integration Trigger:* If `rating == 5` *and* the associated business has Google Integration configured (`google_place_id`, `google_access_token` present), queue a background job (Laravel Queue) to handle the Google submission *workflow*.
        *   **Google Workflow Details:** As noted in API design, direct programmatic submission of *customer* reviews *as* the customer is not standard. The background job should:
            *   Retrieve Google Place ID.
            *   Construct a direct link to the Google review form for that Place ID if possible.
            *   *Alternative:* Notify the vendor (e.g., via email or dashboard notification) that a 5-star review was received and provide the review text/customer name (if allowed) and a link they can share or use to ask the customer for a Google review.
            *   *Most likely interpretation for "Automatically submit":* The backend *prepares* the data and potentially a link, and makes it easy for either the customer (via frontend redirect/prompt) or the vendor (via dashboard action/notification) to complete the process on Google's platform. The `submitted_to_google_at` timestamp on the `reviews` table marks that this process was initiated for *this* review.
4.  **Vendor Dashboard Analytics:**
    *   Query `reviews` table filtered by `business_id`.
    *   Perform aggregations (e.g., `AVG(rating)`, `COUNT(*)`, counts per rating).
    *   Group by `created_at` (day/week/month) for time-series data.
    *   Implement pagination and filtering.
5.  **Subscription Management:**
    *   **Checkout:** Vendor initiates checkout (`POST /subscription`). Create a checkout session with Stripe/PayPal API, redirect vendor to their hosted page.
    *   **Webhooks:** Implement webhook handlers (`POST /webhooks/stripe`, `/webhooks/paypal`).
        *   Listen for `checkout.session.completed`, `customer.subscription.created`, `invoice.payment_succeeded`, `customer.subscription.updated`, `customer.subscription.deleted`, `payment.intent.succeeded` etc.
        *   Verify webhook signature.
        *   Update the `subscriptions` table based on events (status, period end, stripe/paypal IDs).
        *   Update `businesses.current_subscription_id`.
        *   Handle plan changes and cancellations at period end.
    *   **Access Control:** Implement middleware or service logic to check the authenticated vendor's plan (`businesses.current_subscription.plan.features`) before allowing actions (e.g., creating more than max QR codes, accessing analytics beyond plan).
6.  **Email Template & Sending:**
    *   Store templates in `email_templates` table.
    *   Vendor requests send (`POST /email-templates/{id}/send`).
    *   Retrieve template and recipient list.
    *   Queue a background job (Laravel Queue) for each email recipient.
    *   The job renders the template body (using a templating engine like Blade or custom find/replace for `{{variables}}`).
    *   Send email using a transactional email service API (SendGrid, Postmark, Mailgun).
    *   Record each send attempt and status in `email_sends`.

## 4. Security

*   **Authentication:**
    *   Use Supabase Auth for vendor user management (handles password hashing, email verification, etc.).
    *   Laravel will use JWT tokens issued by Supabase Auth for authenticating API requests from vendors.
    *   Public review endpoint requires no authentication but needs protection.
*   **Authorization (Row Level Security - RLS):**
    *   Crucial for multi-tenancy.
    *   Implement Supabase RLS policies to ensure users can only `SELECT`, `INSERT`, `UPDATE`, `DELETE` rows associated with their `business_id`.
    *   Example policy for `reviews`: `(business_id IN ( SELECT businesses.id FROM businesses WHERE (businesses.user_id = auth.uid()) ))`. Apply similar policies across related tables (`qrcodes`, `subscriptions`, `email_templates`, `email_sends`, `review_responses`).
    *   The `users` table itself should have limited public access, maybe just for reading user info related to the authenticated user (`auth.uid() = id`).
*   **Public Endpoint Security (`/reviews/{qr_uuid}/submit`):**
    *   Implement rate limiting based on IP address or other factors to prevent spam/abuse. Laravel's built-in rate limiter is suitable.
    *   Input validation on rating and comment length.
    *   Sanitize user-provided comment text to prevent XSS.
*   **Data Security:**
    *   Sensitive data like Google refresh tokens should be encrypted at rest in the database. Laravel's encryption features can be used before storing in Supabase.
    *   Ensure HTTPS is enforced for all traffic.
*   **Payment Security:**
    *   Never handle raw credit card details on your server. Use Stripe Checkout, Stripe Elements, or PayPal's equivalent hosted/embeddable solutions.
    *   Verify webhook signatures to ensure they are legitimate requests from Stripe/PayPal.
*   **API Security:**
    *   Validate all incoming data using Laravel's validation features.
    *   Protect against common web vulnerabilities (SQL injection - mitigated by using ORM/query builder; CSRF - less relevant for API, but good practice for web forms; XSS - sanitize output).

## 5. Performance

*   **Database Indexing:** Create indexes on foreign keys (`business_id`, `qr_code_id`, `plan_id`, etc.) and frequently queried columns (`created_at` for sorting/filtering, `uuid` on `qrcodes`). Supabase typically adds indexes for PKs and FKs, but verify and add more as needed based on query patterns.
*   **Query Optimization:** Use Laravel Eloquent efficiently. Eager loading (`with()`) to avoid N+1 problems when fetching related data (e.g., reviews with their QR codes, businesses with their subscriptions/plans).
*   **Caching:** Cache aggregated analytics results or frequently accessed, less-changing data (like plans). Laravel provides caching drivers.
*   **Background Jobs:** Delegate time-consuming tasks (sending emails, processing webhooks, Google integration steps) to a queue system (Laravel Queue with Redis or database driver). Use Horizon for monitoring if using Redis.
*   **Rate Limiting:** Protect public and potentially expensive endpoints from excessive requests.
*   **Supabase Performance:** Monitor Supabase performance metrics. Optimize SQL queries run by Supabase. Consider upgrading Supabase plan if performance becomes a bottleneck under load.
*   **CDN:** Use a CDN for serving static assets (QR code images if not generated dynamically by frontend, CSS, JS) to reduce load on the backend server.

## 6. Code Examples

These examples use Laravel Eloquent syntax, assuming standard controller/model structure.

**Example 1: Generating a QR Code (Controller/Service)**

```php
<?php

// In QrCodeService.php (or similar service class)

use App\Models\Business;
use App\Models\QrCode;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB; // For transactions

class QrCodeService
{
    public function generateForBusiness(Business $business, array $data): QrCode
    {
        // Basic plan feature check (simplified)
        if ($business->currentSubscription->plan->features['max_qrcodes'] <= $business->qrcodes()->count()) {
             throw new \Exception("Plan limit reached for QR codes."); // Handle this more gracefully in a real app
        }

        // Generate unique UUID for the public URL
        $uuid = Str::uuid();

        // Construct the review URL (assuming frontend handles this route)
        $reviewUrl = config('app.url') . '/review/' . $uuid;

        try {
            DB::beginTransaction();

            $qrCode = $business->qrcodes()->create([
                'uuid' => $uuid,
                'name' => $data['name'],
                'location_details' => $data['location_details'] ?? null,
                'generated_url' => $reviewUrl,
            ]);

            // Optional: Increment a counter on the business or plan usage
            // $business->increment('qr_code_count'); // If denormalized

            DB::commit();

            return $qrCode;

        } catch (\Exception $e) {
            DB::rollBack();
            // Log the error
            throw $e; // Re-throw or handle
        }
    }
}

// In QrCodeController.php

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateQrCodeRequest; // Custom validation request
use App\Services\QrCodeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth; // Assuming Laravel Auth integrates with Supabase UID

class QrCodeController extends Controller
{
    protected $qrCodeService;

    public function __construct(QrCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
        $this->middleware('auth:sanctum'); // Or whatever guard is used for Supabase JWT
    }

    public function store(CreateQrCodeRequest $request): JsonResponse
    {
        // Get the authenticated user's business (requires RLS or explicit join/check)
        // Assuming User model has a hasOne Business relationship or similar
        $business = Auth::user()->business;

        if (!$business) {
             return response()->json(['message' => 'Business not found for user'], 404);
        }

        try {
            $qrCode = $this->qrCodeService->generateForBusiness($business, $request->validated());

            return response()->json(['qrcode' => $qrCode], 201);

        } catch (\Exception $e) {
            // Handle plan limit exception or other errors
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }
}
```

**Example 2: Submitting a Customer Review (Public Endpoint)**

```php
<?php

// In ReviewController.php

use App\Http\Controllers\Controller;
use App\Http\Requests\SubmitReviewRequest; // Custom validation request
use App\Models\QrCode;
use App\Models\Review;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter; // For rate limiting
use App\Jobs\HandleFiveStarReview; // Background job for Google trigger

class ReviewController extends Controller
{
    public function submit(SubmitReviewRequest $request, string $qrUuid): JsonResponse
    {
        // Apply rate limiting
        $rateLimiterKey = 'submit-review:' . $request->ip();
        if (RateLimiter::tooManyAttempts($rateLimiterKey, 10)) { // 10 attempts per minute per IP
            return response()->json(['message' => 'Too many review submissions from this IP.'], 429);
        }
        RateLimiter::hit($rateLimiterKey, 60); // Increment counter for 60 seconds

        $qrCode = QrCode::where('uuid', $qrUuid)->first();

        if (!$qrCode) {
            return response()->json(['message' => 'QR code not found'], 404);
        }

        // Get the business associated with the QR code
        $business = $qrCode->business; // Assuming QrCode model has belongsTo Business relationship

        if (!$business) {
             // This shouldn't happen if FKs and data integrity are good, but good practice
             Log::error("Business not found for QR Code UUID: " . $qrUuid);
             return response()->json(['message' => 'Internal error processing QR code'], 500);
        }

        try {
            DB::beginTransaction();

            $review = new Review($request->validated());
            $review->qrCode()->associate($qrCode);
            $review->business()->associate($business); // Set business_id explicitly for RLS/queries
            $review->ip_address = $request->ip();
            $review->user_agent = $request->header('User-Agent');
            $review->save();

            // Increment QR code scan/review count (optional, can be calculated via relation)
            $qrCode->increment('scan_count'); // Or review_count specifically

            DB::commit();

            // Trigger Google Integration job if 5 stars and integration is active
            if ($review->rating == 5 && $business->google_place_id && $business->google_access_token) {
                 // Dispatch job to handle the 5-star review workflow in the background
                 HandleFiveStarReview::dispatch($review->id, $business->id);
            }

            // Frontend uses the rating in the response to show conditional feedback
            return response()->json([
                'message' => 'Review submitted successfully',
                'review' => $review
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            RateLimiter::clear($rateLimiterKey); // Clear limiter on error
            Log::error("Review submission failed for QR UUID " . $qrUuid . ": " . $e->getMessage());
            return response()->json(['message' => 'Failed to submit review'], 500);
        }
    }
}

// In HandleFiveStarReview.php (Laravel Job)
<?php

namespace App\Jobs;

use App\Models\Review;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\GoogleMyBusinessService; // Custom service for Google interaction

class HandleFiveStarReview implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reviewId;
    protected $businessId;

    public function __construct(string $reviewId, string $businessId)
    {
        $this->reviewId = $reviewId;
        $this->businessId = $businessId;
    }

    public function handle(GoogleMyBusinessService $googleService): void
    {
        $review = Review::with('business')->find($this->reviewId);

        if (!$review || !$review->business) {
            Log::error("HandleFiveStarReview job failed: Review or Business not found (Review ID: {$this->reviewId})");
            return;
        }

        $business = $review->business;

        // Ensure Google integration is still active and tokens are valid/refreshed
        if (!$business->google_place_id || !$business->google_access_token || $business->google_token_expiry < now()) {
             // Need logic to refresh token or mark integration as invalid
             Log::warning("Google integration not active or tokens expired for business ID: {$business->id}");
             // Potentially dispatch another job to refresh token or notify vendor
             return;
        }

        try {
            // --- Google Integration Strategy (Choose one or combine) ---

            // Strategy A: Generate Google Review Link (most common & supported)
            // The Google My Business API doesn't directly give you a link to *leave* a new review
            // for a specific business. You usually construct a link based on the Place ID.
            $googleReviewLink = $googleService->generateReviewLink($business->google_place_id);

            // Now what? We can't force the customer to click it.
            // Option 1: Update review status and notify vendor. Vendor can then share the link.
            $review->submitted_to_google_at = now(); // Indicates we processed this for Google
            $review->save();
            Log::info("Generated Google review link for review {$review->id}: {$googleReviewLink}");
            // TODO: Add notification logic for vendor

            // Option 2: If the customer is still on the success page, the frontend
            // could potentially fetch this link *after* submission success and show a button.
            // This requires the backend to return the link or have a separate endpoint for it.

            // Strategy B: Reply to the review *on behalf of the vendor* (if review exists on GMB)
            // This is different from submitting a *new* customer review. The API supports this.
            // If the customer *also* left a review on Google and we can match it, we could reply.
            // This is complex and likely out of scope for 2 months unless simplified heavily.

            // Strategy C: Attempt to publish review text to GMB (Less standard, check API docs carefully)
            // Google APIs generally don't allow third parties to publish arbitrary reviews *as* a customer.
            // Avoid this approach unless specific, supported API endpoints are found (unlikely for customer reviews).

            // Sticking with Strategy A: Mark review as processed for Google and notify vendor/make link available.
            // The "automatic submission" is more about automating the *trigger* to *request* or *facilitate* the Google review via a link/prompt.


        } catch (\Exception $e) {
             Log::error("Failed to process 5-star review for Google integration for review {$this->reviewId}: " . $e->getMessage());
             // Handle retries or notify vendor of failure
        }
    }
}
```

**Example 3: Fetching Reviews for Vendor Dashboard (Controller)**

```php
<?php

// In ReviewController.php

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum'); // Protect vendor endpoints
    }

    public function index(Request $request): JsonResponse
    {
        // Get the authenticated user's business
        $business = Auth::user()->business;

        if (!$business) {
             return response()->json(['message' => 'Business not found for user'], 404);
        }

        // Start building the query, applying RLS via Supabase policies
        // or by filtering explicitly on the business_id
        $query = Review::where('business_id', $business->id)
                        ->with('qrCode', 'reviewResponse'); // Eager load relationships

        // Apply filters from request
        if ($request->filled('rating')) {
            $query->where('rating', $request->input('rating'));
        }

        if ($request->filled('qr_code_id')) {
            $query->where('qr_code_id', $request->input('qr_code_id'));
        }

        if ($request->filled('start_date')) {
            $query->where('created_at', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('created_at', '<=', $request->input('end_date') . ' 23:59:59');
        }

        // Apply sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $limit = $request->input('limit', 15); // Default items per page
        $reviews = $query->paginate($limit);

        return response()->json($reviews); // Laravel's paginator returns data and meta fields
    }

    public function show(string $id): JsonResponse
    {
         // Get the authenticated user's business
        $business = Auth::user()->business;

        if (!$business) {
             return response()->json(['message' => 'Business not found for user'], 404);
        }

        // Find the review and ensure it belongs to the business
        // RLS should handle this, but explicit check is safer or required depending on policy
        $review = Review::with('qrCode', 'reviewResponse')
                        ->where('business_id', $business->id) // Explicit check matching RLS
                        ->find($id);

        if (!$review) {
            return response()->json(['message' => 'Review not found or does not belong to your business'], 404);
        }

        return response()->json(['review' => $review]);
    }
}
```

This guide provides a solid foundation for implementing the backend of the QR Review System using Laravel and Supabase. The 2-month timeline is ambitious, requiring careful prioritization, efficient development practices, and potentially deferring less critical features or refining the scope of complex integrations like Google Reviews.
```
