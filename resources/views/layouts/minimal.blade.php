<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="supabase-url" content="{{ config('supabase.url') }}">
    <meta name="supabase-key" content="{{ config('supabase.key') }}">

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
    <link rel="shortcut icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">

    <title>RatingShield | @yield('title', 'QR Review System')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    
    <!-- Supabase JS Library - Load this first without defer -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.38.4/dist/umd/supabase.min.js"></script>
    
    <!-- Alpine.js - Load before our custom scripts to ensure it's available -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Supabase Auth Script - Load after Supabase but before Alpine initialization -->
    <script src="{{ asset('js/supabase-auth.js') }}"></script>
    
    <!-- Other Custom Scripts -->
    <script src="{{ asset('js/app.js') }}" defer></script>
    <script src="{{ asset('js/logout-handler.js') }}"></script>
    <script src="{{ asset('js/auth-manager.js') }}"></script>
    
    <script>
        // Ensure Supabase client is properly initialized before Alpine.js loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded, verifying Supabase client');
            if (!window.supabaseClient && window.supabase && window.supabase.createClient) {
                const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.getAttribute('content');
                const supabaseKey = document.querySelector('meta[name="supabase-key"]')?.getAttribute('content');
                
                if (supabaseUrl && supabaseKey) {
                    console.log('Initializing Supabase client from app layout');
                    window.supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
                }
            }
        });
    </script>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Page Content -->
        <main>
            @yield('content')
        </main>
    </div>
</body>
</html>
