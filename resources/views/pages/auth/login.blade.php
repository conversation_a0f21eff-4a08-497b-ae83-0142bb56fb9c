@extends('layouts.app')

@section('title', 'Login - RatingShield')

@section('content')
<script>
// Ensure Supabase client is initialized on the login page
(function() {
    // Test if Supabase is available
    console.log('Supabase global object available:', !!window.supabase);
    console.log('Supabase createClient available:', !!(window.supabase && window.supabase.createClient));
    console.log('Supabase client already initialized:', !!window.supabaseClient);
    if (!window.supabaseClient && window.supabase && window.supabase.createClient) {
        const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.getAttribute('content');
        const supabaseKey = document.querySelector('meta[name="supabase-key"]')?.getAttribute('content');
        
        if (supabaseUrl && supabaseKey) {
            console.log('Initializing Supabase client directly on login page');
            window.supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
        }
    }
    
    // Set up the login form submission handler without Alpine.js
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const emailInput = document.getElementById('email');
                const passwordInput = document.getElementById('password');
                const rememberInput = document.getElementById('remember-me');
                const errorMessageDiv = document.getElementById('error-message');
                const submitButton = loginForm.querySelector('button[type="submit"]');
                
                // Disable the submit button and hide any previous error messages
                submitButton.disabled = true;
                errorMessageDiv.textContent = '';
                errorMessageDiv.classList.add('hidden');
                
                try {
                    // Check if Supabase client is available
                    if (!window.supabaseClient) {
                        throw new Error('Authentication service is not available. Please try again later.');
                    }
                    
                    const email = emailInput.value;
                    const password = passwordInput.value;
                    
                    console.log('Attempting to sign in with Supabase directly');
                    console.log('Email:', email);
                    
                    // Sign in with Supabase
                    const { data, error } = await window.supabaseClient.auth.signInWithPassword({
                        email: email,
                        password: password
                    });
                    
                    if (error) {
                        throw new Error(error.message || 'Failed to sign in. Please check your credentials.');
                    }
                    
                    console.log('Supabase login successful:', data);
                    if (data && data.session) {
                        const supabaseToken = data.session.access_token;
                        console.log('Token obtained:', supabaseToken.substring(0, 10) + '...');
                        
                        // Remove any existing token input to avoid duplicates
                        const existingToken = loginForm.querySelector('input[name="supabase_token"]');
                        if (existingToken) {
                            existingToken.remove();
                        }
                        
                        // Add the token to the form
                        const tokenInput = document.createElement('input');
                        tokenInput.type = 'hidden';
                        tokenInput.name = 'supabase_token';
                        tokenInput.value = supabaseToken;
                        loginForm.appendChild(tokenInput);
                        
                        // Set a redirect flag to ensure we go to dashboard
                        const redirectInput = document.createElement('input');
                        redirectInput.type = 'hidden';
                        redirectInput.name = 'redirect_to_dashboard';
                        redirectInput.value = 'true';
                        loginForm.appendChild(redirectInput);
                        
                        // Submit the form
                        loginForm.submit();
                    } else {
                        throw new Error('Authentication successful but no session was created. Please try again.');
                    }
                    
                } catch (error) {
                    console.error('Login error:', error);
                    errorMessageDiv.textContent = error.message || 'An unexpected error occurred. Please try again.';
                    errorMessageDiv.classList.remove('hidden');
                    submitButton.disabled = false;
                }
            });
        }
    });
})();
</script>

<div class="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="flex justify-center">
                <a href="{{ route('home') }}">
                    <img class="h-12 w-auto" src="{{ asset('images/logo.svg') }}" alt="RatingShield">
                </a>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Sign in to your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="{{ route('register') }}" class="font-medium text-blue-600 hover:text-blue-500">
                    create a new account
                </a>
            </p>
        </div>

        @if ($errors->any())
            <div class="rounded-md bg-red-50 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            There were errors with your submission
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <form class="mt-8 space-y-6" action="{{ route('login.post') }}" method="POST" id="loginForm">
            @csrf
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only">Email address</label>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Email address">
                </div>
                <div>
                    <label for="password" class="sr-only">Password</label>
                    <input id="password" name="password" type="password" autocomplete="current-password" required
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Password">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember" type="checkbox"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                        Remember me
                    </label>
                </div>

                <div class="text-sm">
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                        Forgot your password?
                    </a>
                </div>
            </div>

            <div>
                <div id="error-message" class="text-red-500 text-sm mb-4 hidden"></div>
                
                <button type="submit"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    Sign in
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
