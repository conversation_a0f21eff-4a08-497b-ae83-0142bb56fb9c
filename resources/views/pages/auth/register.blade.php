@extends('layouts.app')

@section('title', 'Register - RatingShield')

@section('content')
<div class="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="flex justify-center">
                <a href="{{ route('home') }}">
                    <img class="h-12 w-auto" src="{{ asset('images/logo.svg') }}" alt="RatingShield">
                </a>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="{{ route('login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                    sign in to your existing account
                </a>
            </p>
        </div>

        @if ($errors->any())
            <div class="rounded-md bg-red-50 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            There were errors with your submission
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <form class="mt-8 space-y-6" action="{{ route('register.post') }}" method="POST" id="registerForm" x-data="{ 
    name: '', 
    email: '', 
    password: '', 
    password_confirmation: '',
    terms: false,
    loading: false,
    errorMessage: '',
    supabaseToken: '',
    
    async submitForm() {
        console.log('Submit form called');
        this.loading = true;
        this.errorMessage = '';
        
        if (this.password !== this.password_confirmation) {
            this.errorMessage = 'Passwords do not match.';
            this.loading = false;
            return;
        }
        
        if (!this.terms) {
            this.errorMessage = 'You must agree to the Terms of Service and Privacy Policy.';
            this.loading = false;
            return;
        }
        
        try {
            console.log('Attempting to register with Supabase');
            console.log('Email:', this.email);
            console.log('Name:', this.name);
            
            if (typeof window.signUpWithEmail === 'function') {
                // First register with Supabase
                const { data, error } = await window.signUpWithEmail(this.email, this.password, {
                    name: this.name
                });
                
                if (error) {
                    console.error('Supabase registration error:', error);
                    this.errorMessage = error.message || 'Failed to register with Supabase. Please try again.';
                    this.loading = false;
                    return;
                }
                
                if (!data) {
                    console.error('No data returned from Supabase registration');
                    this.errorMessage = 'Failed to register with Supabase. No data returned.';
                    this.loading = false;
                    return;
                }
                
                console.log('Supabase registration successful:', data);
                
                // Get the token from the session if available
                if (data.session && data.session.access_token) {
                    this.supabaseToken = data.session.access_token;
                    console.log('Got access token from session');
                } else {
                    console.warn('No session or access token available');
                    // We'll still try to submit the form without a token
                    // The server will handle this case
                }
            } else {
                console.warn('Supabase signUpWithEmail function not available');
            }
            
            // Then submit the form to Laravel with the Supabase token
            const form = document.getElementById('registerForm');
            
            // Add hidden fields for all relevant data
            const addHiddenField = (name, value) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = name;
                input.value = value || '';
                form.appendChild(input);
            };
            
            // Add token if available
            addHiddenField('supabase_token', this.supabaseToken);
            
            // Always ensure these fields are submitted
            addHiddenField('name', this.name);
            addHiddenField('email', this.email);
            addHiddenField('password', this.password);
            addHiddenField('password_confirmation', this.password_confirmation);
            addHiddenField('terms', this.terms ? '1' : '0');
            
            console.log('Submitting form to Laravel');
            form.submit();
            
        } catch (e) {
            console.error('Registration error:', e);
            this.errorMessage = 'An unexpected error occurred. Please try again.';
            this.loading = false;
        }
    }
}" @submit.prevent="submitForm">
            @csrf
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="name" class="sr-only">Business name</label>
                    <input id="name" name="name" type="text" autocomplete="name" required x-model="name" 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Business name">
                </div>
                <div>
                    <label for="email" class="sr-only">Email address</label>
                    <input id="email" name="email" type="email" autocomplete="email" required x-model="email" 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Email address">
                </div>
                <div>
                    <label for="password" class="sr-only">Password</label>
                    <input id="password" name="password" type="password" autocomplete="new-password" required x-model="password" 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Password">
                </div>
                <div>
                    <label for="password_confirmation" class="sr-only">Confirm Password</label>
                    <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required x-model="password_confirmation" 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Confirm password">
                </div>
            </div>

            <div class="flex items-center">
                <input id="terms" name="terms" type="checkbox" required x-model="terms" 
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="terms" class="ml-2 block text-sm text-gray-900">
                    I agree to the <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a> and <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                </label>
            </div>

            <div>
                <div x-show="errorMessage" class="text-red-500 text-sm mb-4" x-text="errorMessage"></div>
                
                <button type="submit" 
                        :disabled="loading"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    Create Account
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
