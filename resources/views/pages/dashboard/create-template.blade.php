@extends('layouts.app')

@section('title', 'Create Email Template')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Create Email Template</h1>
            <p class="text-gray-600 mt-1">Design a new email template for collecting customer reviews</p>
        </div>
        
        <!-- Form Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <form method="POST" action="{{ route('dashboard.templates.store') }}" class="p-6" enctype="multipart/form-data">
                @csrf
                
                <!-- Error Messages -->
                @if ($errors->any())
                    <div class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
                        <p class="font-bold">Please fix the following errors:</p>
                        <ul class="mt-2 list-disc list-inside">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                
                <!-- Template Name -->
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Post-Purchase Review Request" required>
                    <p class="mt-1 text-sm text-gray-500">Give your template a descriptive name to easily identify it later.</p>
                </div>
                
                <!-- Template Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                    <textarea name="description" id="description" rows="2" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Email template for requesting reviews after customer purchases">{{ old('description') }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Add details about when and how this template will be used.</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Logo Upload -->
                    <div>
                        <label for="logo" class="block text-sm font-medium text-gray-700 mb-1">Logo (Optional)</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="logo" class="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-purple-500">
                                        <span>Upload a file</span>
                                        <input id="logo" name="logo" type="file" class="sr-only" accept="image/*">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Color Selection -->
                    <div>
                        <div class="mb-4">
                            <label for="primary_color" class="block text-sm font-medium text-gray-700 mb-1">Primary Color</label>
                            <div class="flex items-center">
                                <input type="color" name="primary_color" id="primary_color" value="{{ old('primary_color', '#004cbf') }}" class="h-10 w-10 border-gray-300 rounded-md shadow-sm">
                                <input type="text" name="primary_color_hex" id="primary_color_hex" value="{{ old('primary_color', '#004cbf') }}" class="ml-2 shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="#004cbf">
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Main color for your email template</p>
                        </div>
                        
                        <div>
                            <label for="secondary_color" class="block text-sm font-medium text-gray-700 mb-1">Secondary Color</label>
                            <div class="flex items-center">
                                <input type="color" name="secondary_color" id="secondary_color" value="{{ old('secondary_color', '#0dff96') }}" class="h-10 w-10 border-gray-300 rounded-md shadow-sm">
                                <input type="text" name="secondary_color_hex" id="secondary_color_hex" value="{{ old('secondary_color', '#0dff96') }}" class="ml-2 shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="#0dff96">
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Accent color for buttons and highlights</p>
                        </div>
                    </div>
                </div>
                
                <!-- Template Content -->
                <div class="mb-6">
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Email Content</label>
                    <textarea name="content" id="content" rows="10" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Enter your email template content here..." required>{{ old('content', "Dear [Customer Name],\n\nThank you for choosing [Your Business Name]! We hope you had a great experience with us.\n\nWe would greatly appreciate it if you could take a moment to share your feedback. Your opinion helps us improve our services and assists other customers in making informed decisions.\n\nPlease click the button below to leave a review:\n\n[Review Button]\n\nThank you for your support!\n\nBest regards,\n[Your Name]\n[Your Business Name]") }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Use placeholders like [Customer Name] which will be replaced with actual data when the email is sent.</p>
                </div>
                
                <!-- Preview Section -->
                <div class="mb-6 p-4 bg-gray-50 rounded-md">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Template Preview</h3>
                    <p class="text-sm text-gray-500 mb-4">A live preview feature will be available in a future update.</p>
                    
                    <div class="border border-gray-300 rounded-md p-4 bg-white">
                        <div class="flex justify-center mb-4">
                            <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="text-center mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Your Business Name</h4>
                            <p class="text-sm text-gray-500">We value your feedback!</p>
                        </div>
                        <div class="mb-4 text-sm text-gray-700">
                            <p class="mb-2">Dear Customer,</p>
                            <p class="mb-2">Thank you for choosing our business! We hope you had a great experience with us.</p>
                            <p class="mb-2">We would greatly appreciate it if you could take a moment to share your feedback.</p>
                        </div>
                        <div class="flex justify-center mb-4">
                            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Leave a Review
                            </button>
                        </div>
                        <div class="text-xs text-center text-gray-500">
                            <p>Thank you for your support!</p>
                            <p>Best regards, Your Name</p>
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3">
                    <a href="{{ route('dashboard.templates') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                        </svg>
                        Save Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Sync color input with hex text input
    document.addEventListener('DOMContentLoaded', function() {
        const primaryColor = document.getElementById('primary_color');
        const primaryColorHex = document.getElementById('primary_color_hex');
        const secondaryColor = document.getElementById('secondary_color');
        const secondaryColorHex = document.getElementById('secondary_color_hex');
        
        primaryColor.addEventListener('input', function() {
            primaryColorHex.value = this.value;
        });
        
        primaryColorHex.addEventListener('input', function() {
            primaryColor.value = this.value;
        });
        
        secondaryColor.addEventListener('input', function() {
            secondaryColorHex.value = this.value;
        });
        
        secondaryColorHex.addEventListener('input', function() {
            secondaryColor.value = this.value;
        });
    });
</script>
@endsection
