@extends('layouts.app')

@section('title', 'Email Templates')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Email Templates</h1>
                <p class="text-gray-600 mt-1">Create and manage email templates for review collection</p>
            </div>
            <a href="{{ route('dashboard.templates.create') }}" class="inline-flex items-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-white tracking-wider hover:bg-purple-700 active:bg-purple-800 focus:outline-none focus:border-purple-800 focus:ring focus:ring-purple-200 transition ease-in-out duration-150">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Template
            </a>
        </div>
        
        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md" role="alert">
            <p>{{ session('success') }}</p>
        </div>
        @endif
        
        <!-- Templates List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            @if(isset($templates) && count($templates) > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                    @foreach($templates as $template)
                        <div class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition duration-150">
                            <!-- Template Preview -->
                            <div class="h-48 bg-gray-100 relative overflow-hidden">
                                <!-- Template color preview with logo placeholder -->
                                <div class="absolute inset-0" style="background-color: {{ $template->primary_color }}">
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        @if($template->logo)
                                            <img src="{{ asset('storage/logos/' . $template->logo) }}" alt="{{ $template->name }}" class="max-h-16">
                                        @else
                                            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Status badge -->
                                <div class="absolute top-2 right-2">
                                    @if($template->is_active)
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                    @else
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Template Info -->
                            <div class="p-4">
                                <h3 class="text-lg font-medium text-gray-900 mb-1">{{ $template->name }}</h3>
                                <p class="text-sm text-gray-500 mb-3 line-clamp-2">{{ $template->description }}</p>
                                
                                <div class="flex justify-between items-center text-sm text-gray-500">
                                    <div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            {{ $template->view_count }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            {{ $template->submission_count }}
                                        </div>
                                    </div>
                                    <div>{{ $template->created_at->format('M d, Y') }}</div>
                                </div>
                            </div>
                            
                            <!-- Template Actions -->
                            <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-between">
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800">Edit</a>
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800">Preview</a>
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800">Send</a>
                                <a href="#" class="text-sm text-red-600 hover:text-red-800">Delete</a>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    {{ $templates->links() }}
                </div>
            @else
                <div class="p-6 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new email template.</p>
                    <div class="mt-6">
                        <a href="{{ route('dashboard.templates.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create Template
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
