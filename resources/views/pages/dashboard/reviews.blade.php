@extends('layouts.app')

@section('title', 'Reviews')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Reviews</h1>
            <p class="text-gray-600 mt-1">View and manage customer reviews</p>
        </div>
        
        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md" role="alert">
            <p>{{ session('success') }}</p>
        </div>
        @endif
        
        <!-- Error Message -->
        @if(session('error'))
        <div class="mb-8 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
            <p>{{ session('error') }}</p>
        </div>
        @endif
        
        <!-- Filters -->
        <div class="mb-8 bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Filter Reviews</h2>
            <form action="{{ route('dashboard.reviews') }}" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Rating Filter -->
                <div>
                    <label for="rating" class="block text-sm font-medium text-gray-700 mb-1">Rating</label>
                    <select id="rating" name="rating" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Ratings</option>
                        <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 Stars</option>
                        <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 Stars</option>
                        <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 Stars</option>
                        <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 Stars</option>
                        <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 Star</option>
                    </select>
                </div>
                
                <!-- Date Range Filter -->
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start_date" name="start_date" value="{{ request('start_date') }}" class="mt-1 block w-full border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                </div>
                
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end_date" name="end_date" value="{{ request('end_date') }}" class="mt-1 block w-full border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                </div>
                
                <!-- Filter Actions -->
                <div class="md:col-span-3 flex justify-end space-x-3">
                    <a href="{{ route('dashboard.reviews') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Reset
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Reviews List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            @if(isset($reviews) && count($reviews) > 0)
                <div class="divide-y divide-gray-200">
                    @foreach($reviews as $review)
                        <div class="p-6">
                            <div class="flex flex-col md:flex-row md:justify-between md:items-start">
                                <!-- Review Content -->
                                <div class="flex-grow mb-4 md:mb-0 md:mr-6">
                                    <div class="flex items-center mb-2">
                                        <div class="flex mr-2">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $review->rating)
                                                    <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                @else
                                                    <svg class="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                @endif
                                            @endfor
                                        </div>
                                        <span class="text-sm text-gray-500">{{ $review->created_at->format('M d, Y') }}</span>
                                    </div>
                                    
                                    <h3 class="text-lg font-medium text-gray-900 mb-1">{{ $review->customer_name }}</h3>
                                    <p class="text-gray-600 mb-3">{{ $review->customer_email }}</p>
                                    
                                    <div class="mb-4">
                                        <p class="text-gray-900">{{ $review->comment }}</p>
                                        @if($review->additional_feedback)
                                            <div class="mt-2 p-3 bg-gray-50 rounded-md">
                                                <p class="text-sm text-gray-600"><strong>Additional Feedback:</strong> {{ $review->additional_feedback }}</p>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    @if($review->vendor_response)
                                        <div class="mt-4 p-4 bg-blue-50 rounded-md">
                                            <h4 class="text-sm font-medium text-blue-800 mb-1">Your Response:</h4>
                                            <p class="text-sm text-blue-700">{{ $review->vendor_response }}</p>
                                        </div>
                                    @endif
                                </div>
                                
                                <!-- Review Actions -->
                                <div class="flex-shrink-0 w-full md:w-64">
                                    @if(!$review->vendor_response)
                                        <div class="p-4 bg-gray-50 rounded-md">
                                            <h4 class="text-sm font-medium text-gray-900 mb-2">Respond to Review</h4>
                                            <form action="{{ route('dashboard.reviews.respond', $review->id) }}" method="POST">
                                                @csrf
                                                <textarea name="vendor_response" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md mb-2" placeholder="Type your response here..." required></textarea>
                                                <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                                    </svg>
                                                    Submit Response
                                                </button>
                                            </form>
                                        </div>
                                    @else
                                        <div class="p-4 bg-gray-50 rounded-md text-center">
                                            <svg class="mx-auto h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <p class="mt-2 text-sm text-gray-600">You've already responded to this review</p>
                                            <button type="button" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                Edit Response
                                            </button>
                                        </div>
                                    @endif
                                    
                                    @if($review->rating == 5 && !$review->is_published_to_google)
                                        <div class="mt-3 p-4 bg-yellow-50 rounded-md">
                                            <h4 class="text-sm font-medium text-yellow-800 mb-2">Google Reviews</h4>
                                            <p class="text-xs text-yellow-700 mb-2">This 5-star review can be published to Google Reviews.</p>
                                            <button type="button" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                Publish to Google
                                            </button>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="px-6 py-4 bg-gray-50">
                    {{ $reviews->appends(request()->query())->links() }}
                </div>
            @else
                <div class="p-6 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No reviews found</h3>
                    <p class="mt-1 text-sm text-gray-500">Once customers scan your QR codes and leave reviews, they will appear here.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
