@extends('layouts.app')

@section('title', 'Analytics')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Analytics</h1>
            <p class="text-gray-600 mt-1">Track your review performance and customer feedback</p>
        </div>
        
        <!-- Analytics Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Monthly Reviews Chart -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Monthly Reviews</h2>
                <div class="h-80">
                    <!-- Chart placeholder - In a real implementation, this would use a JS chart library -->
                    <div class="h-full bg-gray-50 rounded-lg flex flex-col">
                        <div class="flex-1 p-4">
                            <div class="h-full flex items-end space-x-4">
                                @foreach($monthlyReviews as $monthData)
                                    <div class="flex flex-col items-center flex-1">
                                        <div class="w-full bg-blue-500 rounded-t-sm" style="height: {{ min(100, max(5, ($monthData['count'] / max(1, max(array_column($monthlyReviews, 'count')))) * 100) }}%"></div>
                                        <div class="text-xs text-gray-500 mt-2">{{ $monthData['count'] }}</div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="px-4 py-2 flex justify-between">
                            @foreach($monthlyReviews as $monthData)
                                <div class="text-xs text-gray-500">{{ $monthData['month'] }}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-sm text-gray-500">
                    <p>Total reviews in the last 6 months: {{ array_sum(array_column($monthlyReviews, 'count')) }}</p>
                </div>
            </div>
            
            <!-- Rating Distribution -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Rating Distribution</h2>
                <div class="h-80">
                    <!-- Chart placeholder - In a real implementation, this would use a JS chart library -->
                    <div class="h-full flex flex-col justify-center">
                        @php
                            $totalRatings = array_sum($ratingDistribution);
                        @endphp
                        
                        @for($i = 5; $i >= 1; $i--)
                            <div class="flex items-center mb-4">
                                <div class="w-16 text-sm font-medium text-gray-900">
                                    {{ $i }} @if($i == 1) Star @else Stars @endif
                                </div>
                                <div class="flex-1 ml-4">
                                    <div class="h-5 bg-gray-200 rounded-full overflow-hidden">
                                        @php
                                            $percentage = $totalRatings > 0 ? ($ratingDistribution[$i] / $totalRatings) * 100 : 0;
                                        @endphp
                                        <div class="h-full bg-yellow-400 rounded-full" style="width: {{ $percentage }}%"></div>
                                    </div>
                                </div>
                                <div class="ml-4 w-16 text-sm text-gray-500">
                                    {{ $ratingDistribution[$i] }} ({{ $totalRatings > 0 ? round($percentage) : 0 }}%)
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>
                <div class="mt-4 text-sm text-gray-500">
                    <p>Total ratings: {{ $totalRatings }}</p>
                    <p>Average rating: {{ $totalRatings > 0 ? number_format(array_sum(array_map(function($rating, $count) { return $rating * $count; }, array_keys($ratingDistribution), array_values($ratingDistribution))) / $totalRatings, 1) : 0 }}/5</p>
                </div>
            </div>
        </div>
        
        <!-- Additional Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- QR Code Performance -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">QR Code Performance</h2>
                <div class="text-center py-8">
                    <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-4 text-sm text-gray-500">QR code performance analytics will be available in a future update.</p>
                </div>
            </div>
            
            <!-- Customer Sentiment -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Customer Sentiment</h2>
                <div class="text-center py-8">
                    <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="mt-4 text-sm text-gray-500">Sentiment analysis will be available in a future update.</p>
                </div>
            </div>
            
            <!-- Google Reviews Integration -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Google Reviews</h2>
                <div class="text-center py-8">
                    <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="mt-4 text-sm text-gray-500">Google Reviews integration analytics will be available in a future update.</p>
                </div>
            </div>
        </div>
        
        <!-- Export Options -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Export Reports</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 border border-gray-200 rounded-md hover:bg-gray-50 transition duration-150 cursor-pointer">
                    <div class="flex items-center">
                        <svg class="h-8 w-8 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">Monthly Report</h3>
                            <p class="text-xs text-gray-500">Export a summary of this month's reviews</p>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-md hover:bg-gray-50 transition duration-150 cursor-pointer">
                    <div class="flex items-center">
                        <svg class="h-8 w-8 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">Custom Date Range</h3>
                            <p class="text-xs text-gray-500">Export reviews from a specific period</p>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-md hover:bg-gray-50 transition duration-150 cursor-pointer">
                    <div class="flex items-center">
                        <svg class="h-8 w-8 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">All Reviews</h3>
                            <p class="text-xs text-gray-500">Export all reviews in CSV format</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
