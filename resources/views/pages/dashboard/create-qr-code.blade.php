@extends('layouts.app')

@section('title', 'Create QR Code')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Create QR Code</h1>
            <p class="text-gray-600 mt-1">Generate a new QR code for customer reviews</p>
        </div>
        
        <!-- Form Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <form method="POST" action="{{ route('dashboard.qr-codes.store') }}" class="p-6">
                @csrf
                
                <!-- Error Messages -->
                @if ($errors->any())
                    <div class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
                        <p class="font-bold">Please fix the following errors:</p>
                        <ul class="mt-2 list-disc list-inside">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                
                <!-- QR Code Name -->
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">QR Code Name</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Store Entrance QR Code" required>
                    <p class="mt-1 text-sm text-gray-500">Give your QR code a descriptive name to easily identify it later.</p>
                </div>
                
                <!-- QR Code Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                    <textarea name="description" id="description" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Located at the entrance of our main store">{{ old('description') }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Add details about where this QR code will be placed or its purpose.</p>
                </div>
                
                <!-- Customization Options (Placeholder for future implementation) -->
                <div class="mb-6 p-4 bg-gray-50 rounded-md">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Customization Options</h3>
                    <p class="text-sm text-gray-500 mb-4">These options will be available in a future update.</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 opacity-50 pointer-events-none">
                        <!-- Logo Upload -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Logo (Coming Soon)</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <p class="text-xs text-gray-500">Upload a logo to display in the center of your QR code</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Color Options -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Colors (Coming Soon)</label>
                            <div class="grid grid-cols-3 gap-2">
                                <div class="h-8 rounded-md bg-blue-500"></div>
                                <div class="h-8 rounded-md bg-green-500"></div>
                                <div class="h-8 rounded-md bg-purple-500"></div>
                                <div class="h-8 rounded-md bg-red-500"></div>
                                <div class="h-8 rounded-md bg-yellow-500"></div>
                                <div class="h-8 rounded-md bg-gray-900"></div>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Choose custom colors for your QR code</p>
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3">
                    <a href="{{ route('dashboard.qr-codes') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Generate QR Code
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
