@extends('layouts.minimal')

@section('title', 'Leave a Review')

@section('content')
<div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
    <div class="relative py-3 sm:max-w-xl sm:mx-auto">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-green-400 shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl"></div>
        <div class="relative px-4 py-10 bg-white shadow-lg sm:rounded-3xl sm:p-20">
            <div class="max-w-md mx-auto">
                <div class="flex items-center justify-center">
                    <h1 class="text-2xl font-semibold text-center">{{ $businessName }}</h1>
                </div>
                <div class="divide-y divide-gray-200">
                    <div class="py-8 text-base leading-6 space-y-4 text-gray-700 sm:text-lg sm:leading-7">
                        <p class="text-center">We value your feedback! Please take a moment to share your experience.</p>
                        
                        <form action="{{ route('review.store', ['id' => $qrCode['uuid']]) }}" method="POST" class="space-y-6">
                            @csrf
                            
                            <div>
                                <label for="customer_name" class="block text-sm font-medium text-gray-700">Your Name</label>
                                <div class="mt-1">
                                    <input type="text" name="customer_name" id="customer_name" required class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="{{ old('customer_name') }}">
                                </div>
                                @error('customer_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="customer_email" class="block text-sm font-medium text-gray-700">Your Email</label>
                                <div class="mt-1">
                                    <input type="email" name="customer_email" id="customer_email" required class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="{{ old('customer_email') }}">
                                </div>
                                @error('customer_email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="rating-container flex items-center">
                                        @for ($i = 5; $i >= 1; $i--)
                                            <input type="radio" id="rating-{{ $i }}" name="rating" value="{{ $i }}" class="hidden" {{ old('rating') == $i ? 'checked' : '' }}>
                                            <label for="rating-{{ $i }}" class="cursor-pointer text-3xl text-gray-300 hover:text-yellow-400 peer-checked:text-yellow-400">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            </label>
                                        @endfor
                                    </div>
                                </div>
                                @error('rating')
                                    <p class="mt-1 text-sm text-red-600 text-center">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="comment" class="block text-sm font-medium text-gray-700">Comment</label>
                                <div class="mt-1">
                                    <textarea name="comment" id="comment" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">{{ old('comment') }}</textarea>
                                </div>
                                @error('comment')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div id="additional-feedback-container" class="hidden">
                                <label for="additional_feedback" class="block text-sm font-medium text-gray-700">What could we improve?</label>
                                <div class="mt-1">
                                    <textarea name="additional_feedback" id="additional_feedback" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">{{ old('additional_feedback') }}</textarea>
                                </div>
                                @error('additional_feedback')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="pt-5">
                                <div class="flex justify-center">
                                    <button type="submit" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Submit Review
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ratingInputs = document.querySelectorAll('input[name="rating"]');
        const additionalFeedbackContainer = document.getElementById('additional-feedback-container');
        
        // Show/hide additional feedback based on rating
        ratingInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                if (parseInt(this.value) < 5) {
                    additionalFeedbackContainer.classList.remove('hidden');
                } else {
                    additionalFeedbackContainer.classList.add('hidden');
                }
            });
        });
        
        // Initialize based on any pre-selected rating (e.g., from validation errors)
        const selectedRating = document.querySelector('input[name="rating"]:checked');
        if (selectedRating && parseInt(selectedRating.value) < 5) {
            additionalFeedbackContainer.classList.remove('hidden');
        }
        
        // Star rating hover effect
        const ratingLabels = document.querySelectorAll('.rating-container label');
        
        ratingLabels.forEach(function(label, index) {
            label.addEventListener('mouseover', function() {
                // Highlight current and previous stars
                for (let i = 0; i <= index; i++) {
                    ratingLabels[i].classList.add('text-yellow-400');
                }
                
                // Remove highlight from next stars
                for (let i = index + 1; i < ratingLabels.length; i++) {
                    ratingLabels[i].classList.remove('text-yellow-400');
                }
            });
            
            label.addEventListener('mouseout', function() {
                // Reset all stars
                ratingLabels.forEach(function(label) {
                    label.classList.remove('text-yellow-400');
                });
                
                // Re-highlight based on selected rating
                const selectedRating = document.querySelector('input[name="rating"]:checked');
                if (selectedRating) {
                    const selectedIndex = parseInt(selectedRating.value) - 1;
                    for (let i = 0; i <= selectedIndex; i++) {
                        ratingLabels[i].classList.add('text-yellow-400');
                    }
                }
            });
        });
    });
</script>
@endsection
