<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ReviewController;
use App\Http\Middleware\SupabaseAuth;

// Landing page route
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->name('login.post');
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register'])->name('register.post');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// API routes for Supabase integration
Route::get('/api/user', [AuthController::class, 'user'])->name('api.user');

// Review routes (public)
Route::get('/review/{id}', [ReviewController::class, 'show'])->name('review.show');
Route::post('/review/{id}', [ReviewController::class, 'store'])->name('review.store');
Route::get('/review/{id}/thank-you', [ReviewController::class, 'thankYou'])->name('review.thank-you');
Route::post('/review/{id}/google', [ReviewController::class, 'submitToGoogle'])->name('review.submit-to-google');

// Dashboard routes (protected by SupabaseAuth middleware)
Route::middleware([SupabaseAuth::class])->group(function () {
    // Main dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // QR Code management
    Route::get('/dashboard/qr-codes', [DashboardController::class, 'qrCodes'])->name('dashboard.qr-codes');
    Route::get('/dashboard/qr-codes/create', [DashboardController::class, 'createQrCode'])->name('dashboard.qr-codes.create');
    Route::post('/dashboard/qr-codes', [DashboardController::class, 'storeQrCode'])->name('dashboard.qr-codes.store');
    
    // Review management
    Route::get('/dashboard/reviews', [DashboardController::class, 'reviews'])->name('dashboard.reviews');
    Route::post('/dashboard/reviews/{id}/respond', [DashboardController::class, 'respondToReview'])->name('dashboard.reviews.respond');
    
    // Analytics
    Route::get('/dashboard/analytics', [DashboardController::class, 'analytics'])->name('dashboard.analytics');
    
    // Template management
    Route::get('/dashboard/templates', [DashboardController::class, 'templates'])->name('dashboard.templates');
    Route::get('/dashboard/templates/create', [DashboardController::class, 'createTemplate'])->name('dashboard.templates.create');
    Route::post('/dashboard/templates', [DashboardController::class, 'storeTemplate'])->name('dashboard.templates.store');
});
