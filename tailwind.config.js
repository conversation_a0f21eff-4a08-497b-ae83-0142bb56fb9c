/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6f0ff',
          100: '#cce0ff',
          200: '#99c2ff',
          300: '#66a3ff',
          400: '#3385ff',
          500: '#004cbf', /* Logo blue color */
          600: '#003d99',
          700: '#002e73',
          800: '#001f4d',
          900: '#000f26',
          950: '#000a1a',
        },
        secondary: {
          50: '#e6fff6',
          100: '#ccffed',
          200: '#99ffdb',
          300: '#66ffc9',
          400: '#33ffb7',
          500: '#0dff96', /* Logo green color */
          600: '#00cc78',
          700: '#00995a',
          800: '#00663c',
          900: '#00331e',
          950: '#00190f',
        },
      },
    },
  },
  plugins: [],
}
